export const API = {
  CREATE_GOOGLE_FORM: '/api/googleforms/createForm',
  AUTH_GOOGLE_FORM: '/api/googleforms/auth',
  GOOGLE_FORM: '/api/googleforms/createForm',

  LOGIN_GOOGLE: '/api/users/google',
  LOGIN: '/api/users/login',
  LOGOUT: '/api/users/logout',
  MY_INFO: '/api/users/me',

  USER_RESET_PASSWORD: '/api/users/resetPassword',
  USER_CHANGE_PASSWORD: '/api/users/changePassword',
  USER_FORGET_PASSWORD: '/api/users/forgotPasswordMail',
  USER_REFRESH_TOKEN: '/api/users/generateAccessToken',

  USERS: '/api/users',
  USER_ID: '/api/users/{0}',
  REMOVE_USER_ORG: '/api/users/{0}/removeFromOrganization',
  USER_MANAGER_ID: '/api/users/manager/{0}',
  REGISTER_USER: '/api/users/register',
  CONFIRM_REGISTER: '/api/users/confirmRegister',
  CONFIRM_INVITATION: '/api/users/confirmInvitation',
  REJECT_INVITATION: '/api/users/rejectInvitation',
  USER_DEVELOPER: '/api/users/{0}/developer',
  USER_INFO: '/api/users/info',
  USER_ACTIVE_ACCOUNT: '/api/users/activeAccount',
  USER_FIND_ONE: '/api/users/findOne',
  USER_ORGANIZATION: '/api/users/getListUserByOrganization',

  STREAM_ID: '/api/files/content/{0}',
  FILE_ID: '/api/files/{0}',
  FILE_EXIST: '/api/files/{0}/exists',

  ROLES: '/api/roles',
  ROLES_ID: '/api/roles/{0}',

  PERMISSION: '/api/permissions',
  PERMISSION_ID: '/api/permissions/{0}',
  UPLOAD_IMAGE: '/upload/image',
  UPLOAD_FILE: '/upload/file',
  UPLOAD_VOICE: '/upload/uploadVoices',

  PERMISSION_ACTION: '/api/permissions/actions',

  FOLDER: '/api/folders',
  EXAM_SCHOOL_FOLDER: '/api/folders/getExamSchoolFolder',
  FOLDER_MANAGER: '/api/folders/manager',
  FOLDER_ID: '/api/folders/{0}',
  FOLDER_DETAIL_ID: '/api/folders/{0}/detail',
  FOLDER_COPY: '/api/folders/copy',
  FOLDER_AVAILABLE: '/api/folders/available',

  PROJECT: '/api/projects',
  PROJECT_CREATE_FROM_TOOL: '/api/projects/createFromTool',
  PROJECT_DETAIL: '/api/projects/{0}/details',
  PROJECT_COPY: '/api/projects/copy',
  PROJECT_SUBMIT_INPUT_DATA_STREAM: '/api/projects/{0}/submitInputDataStream',
  PROJECT_ID: '/api/projects/{0}',
  PROJECT_RECENT: '/api/projects/recents',
  PROJECT_MOVE: '/api/projects/{0}/move',
  PROJECT_CREATE_CONTENT: '/api/projects/{0}/createContentBlock',
  PROJECT_INSERT_CONTENT: '/api/projects/{0}/insertContentBlock',
  PROJECT_CREATE_CONTENT_FROM_TEMPLATE: '/api/projects/{0}/createContentsFromTemplate',
  RECENTS: '/api/recents/recently',
  REMOVE_PROJECT_THUMBNAIL: '/api/projects/{0}/removeThumbnail',
  STREAM_MARK_TEST: '/api/projects/{0}/stream-mark-test',
  PROJECT_STUDENT_TAG_CATEGORY: '/api/projects/studentProjectTagCategories',

  INPUT: '/api/inputs',
  INPUT_ID: '/api/inputs/{0}',
  INPUT_UPDATE_MARK: '/api/inputs/{0}/updateInputMarkTest',

  CONTENT: '/api/contents',
  CONTENT_ID: '/api/contents/{0}',
  CONTENT_BY_PROJECT_ID: '/api/contents/project/{0}',
  CONTENT_SUBMIT_INPUT_DATA: '/api/contents/{0}/submitInputData',
  CONTENT_BLOCK: '/api/projects/{0}/createContentBlock',
  CONTENT_MOVE_DOWN: '/api/contents/{0}/moveDown',
  CONTENT_MOVE_UP: '/api/contents/{0}/moveUp',
  CONTENT_MOVE_TO_INDEX: '/api/contents/{0}/moveContent',
  CONTENT_SUBMIT_EXAM: '/api/contents/submitExam',
  CONTENT_SUBMIT_MARK: '/api/contents/submitMarkTests',
  CONTENT_SUBMIT_SPEAKING: '/api/contents/submitSpeaking',
  CONTENT_GENERATE_TOPIC: '/api/contents/generateTopic',

  RESPONSE: '/api/responses',
  RESPONSE_ID: '/api/responses/{0}',
  RESPONSE_OUTPUT_ID: '/api/responses/{0}/output',
  RETRY_RESPONSE: '/api/responses/{0}/retrySubmit',
  CANCEL_RETRY_RESPONSE: '/api/responses/{0}/cancelSubmit',
  RESPONSE_STREAM_CONTENT: '/api/responses/{0}/stream-content',
  ACTIVATE_RESPONSE: '/api/responses/{0}/activate',
  DELETE_MARK_TEST_RESPONSE: '/api/responses/{0}/deleteMarkTestResponse',

  SHARE: '/api/share',
  SHARED_WITH_ME: '/api/share/allSharedWithMe',
  SHARE_SUBMIT: '/api/share/submit',
  SHARE_PROJECT: '/api/share/project',
  SHARE_FOLDER: '/api/share/folder',
  SHARE_WORKSPACE: '/api/share/workspace',

  GENERAL_ACCESS: '/api/generalAccess',
  GENERAL_ACCESS_RESTRICTED: '/api/generalAccess/restricted',
  GENERAL_ACCESS_LINK: '/api/generalAccess/link',
  GENERAL_ACCESS_ORGANIZATIONAL: '/api/generalAccess/organizational',
  ORGANIZATION_USER: '/api/organizationUsers',
  ORGANIZATION_USER_ID: '/api/organizationUsers/{0}',
  ORGANIZATION: '/api/organizations',
  ORGANIZATION_ID: '/api/organizations/{0}/',
  ORGANIZATION_DETAIL: '/api/organizations/{0}/detail',
  ORGANIZATION_MANAGER: '/api/organizations/manager',

  TOOL: '/api/tools',
  ALL_TOOL: '/api/tools/allTools',
  TOOL_AVAILABLE: '/api/tools/availableTools',
  TOOL_ID: '/api/tools/{0}',
  TOOL_GROUP: '/api/toolgroups',
  TOOL_GROUP_ID: '/api/toolgroups/{0}',
  TOOL_WELCOME: '/api/tools/welcome',
  TOOL_FAVORITE: '/api/tools/favorite',
  TOOL_UNFAVORITE: '/api/tools/{0}/favorite',
  TOOL_MARK_TEST: '/api/tools/markTestTools',
  TOOL_MOST_USED: '/api/tools/getToolByMostUsed',

  REPORT: '/api/reports',
  CREATE_PROJECT_FILE: '/api/reports/createProjectFile',
  CREATE_ONE_EXAM: '/api/reports/createOneExam',
  CREATE_MULTI_EXAM: '/api/reports/createMultiExam',
  CREATE_ONE_MARK_TEST: '/api/reports/createOneMarkTest',
  CREATE_MULTI_MARK_TESTS: '/api/reports/createMultiMarkTests',
  DOWNLOAD_REPORT_FILE: '/api/reports/{0}/download?displayName={1}',
  DOWNLOAD_ACADEMIC_REPORT: '/api/reports/createAcademicReport',
  DOWNLOAD_SPEAKING_REPORT: '/api/reports/createSpeakingReport',

  WORKSPACE: '/api/workspaces',
  WORKSPACE_ID: '/api/workspaces/{0}/details',
  WORKSPACE_AVAILABLE: '/api/workspaces/availableForUser',
  WORKSPACE_BELONGS: '/api/workspaces/{0}/belongs',

  VIDEO: '/api/videos',
  VIDEO_DETAIL: '/api/videos/video-detail',

  IMAGE_EXTRACT_TEXT: '/upload/imageToText',
  IMAGE_DESCRIPTION: '/upload/imageToDescription',
  UPLOAD_FILE_EXTRACT_TEXT: '/upload/extractText',
  FILE_ID_EXTRACT_TEXT: '/api/files/{0}/extractText',

  PROJECT_PERMISSION: '/api/projects/{0}/permission',
  FOLDER_PERMISSION: '/api/folders/{0}/permission',

  INSTRUCTION: '/api/instructions',
  INSTRUCTION_ID: '/api/instructions/{0}',
  INSTRUCTION_DETAIL_ID: '/api/instructions/{0}/details',
  COPY_INSTRUCTION: '/api/instructions/copy',

  OPTION: '/api/options',
  OPTION_ID: '/api/options/{0}',
  SAMPLE: '/api/samples',
  SAMPLE_ID: '/api/samples/{0}',
  OWLEE_URL: '{0}/chatbot-iframe/{1}',
  OWLEE_GET_AVATAR_INFO: '{0}/api/chatbot/avatar/{1}',
  DATASET: '/api/datasets',
  DATASET_ID: '/api/datasets/{0}',
  CONVERSATION: '/api/conversations',
  CONVERSATION_DETAILS: '/api/conversations/{0}',
  CONVERSATION_DETAIL_APPROVE: '/api/conversations/{0}/approve',
  OUTPUT_TYPE: '/api/outputtypes',
  OUTPUT_TYPE_ID: '/api/outputtypes/{0}',

  FINE_TUNING: '/api/finetuning',
  FINE_TUNING_ID: '/api/finetuning/{0}',
  CREATE_FINE_TUNING: '/api/finetuning/finetuning',

  USER_TOOL: '/api/userTools/{0}/tools',
  USER_TOOL_ID: '/api/userTools/{0}',
  UNSAVED: '/api/mySaved/unsave',
  SAVED: '/api/mySaved/save',
  MY_SAVED: '/api/mySaved/all',

  KNOWLEDGE: '/api/knowledge',
  KNOWLEDGE_ID: '/api/knowledge/{0}',

  TEMPLATE: '/api/templates',
  TEMPLATE_WELCOME: '/api/templates/welcome',
  TEMPLATE_UPLOAD_THUMBNAIL: '/upload/imageTemplate',
  TEMPLATE_AVAILABLE: '/api/templates/available',
  TEMPLATE_ID: '/api/templates/{0}',
  TEMPLATE_NEW_CONTENT: '/api/templates/{0}/newContent',
  CREATE_PROJECT_FROM_TEMPLATE: '/api/projects/createFromTemplate',
  PACKAGE: '/api/packages',
  PACKAGE_ID: '/api/packages/{0}',
  SUBSCRIPTION: '/api/subscriptions/customer',
  SUBSCRIPTION_ID: '/api/subscriptions/customer/{0}',
  SUBSCRIPTION_CURRENT_PACKAGE: '/api/subscriptions/currentPackage',
  SUBSCRIPTION_ORDER_STUDENT: '/api/subscriptions/order/student',
  FEATURES: '/api/features',

  IMAGE: '/api/images',
  IMAGE_ID: '/api/images/{0}',
  DISCOUNT_CHECK: '/api/discounts/check/',
  GET_LINK_VNPAY: '/api/subscriptions/order',
  BUY_MORE_ADD_ON: '/api/subscriptions/buyMore',
  TRANSACTION_PAYMENT_STREAM: '/api/transactions/{0}/stream-payment',

  RESOURCE: '/api/resources',
  RESOURCE_ID: '/api/resources/{0}',
  RESOURCE_AVAILABLE: '/api/resources/available',
  RESOURCE_VIDEO: '/api/resources/video',
  RESOURCE_UPLOAD: '/upload/resource',
  RESOURCE_UPLOAD_VIDEO: '/upload/offlinevideo',

  RESOURCE_CAPACITY: '/api/resources/capacity',
  RESOURCE_CHECK_EXIST: '/api/resources/{0}/check',

  UPLOAD_IMAGE_PROJECT: '/upload/imageProject',
  DASHBOARD_MEDIA_USED: '/api/dashboards/mediaUsed',
  DASHBOARD_TOOL_USED: '/api/dashboards/toolsUsed',
  DASHBOARD_TOOL_SUBMITTED: '/api/dashboards/toolSubmited',
  DASHBOARD_PROJECT: '/api/dashboards/project',

  ORG_DASHBOARD_TOP_TOOL_USED: '/api/dashboards/topToolsUsedForOrganization',
  ORG_DASHBOARD_TOOL_USED: '/api/dashboards/toolsUsedForOrganization',
  ORG_DASHBOARD_TOOL_SUBMITTED: '/api/dashboards/toolSubmitedForOrganization',
  ORG_DASHBOARD_PROJECT: '/api/dashboards/projectForOrganization',

  TRANSACTION_ID: '/api/transactions/{0}',
  PAYMENT_HISTORY: '/api/transactions/paymentHistory',
  PAYMENT_HISTORY_USER_ID: '/api/transactions/{0}/paymentHistory',

  SUBSCRIPTION_DATE_INFO: '/api/subscriptions/dateInfo',

  INVITATION: '/api/invitations',
  INVITATION_ID: '/api/invitations/{0}',
  DASHBOARD_TOP_TOOL_USED: '/api/dashboards/topToolsUsed',
  TOKEN_BY_USER: '/api/dashboards/tokensByUser',
  RESEND_INVITATION: '/api/invitations/resend',
  TOOL_TOKEN: '/api/dashboards/toolTokens',
  STUDENT_TOOL_COST: '/api/dashboards/studentToolCost',
  OPENAI_COST: '/api/dashboards/openAICost',
  SAMPLE_CONTENT_GET_ONE: '/api/samplecontents/{0}/getOne',
  SAVE_SAMPLE_CONTENT: '/api/samplecontents/save',

  STREAM_MEDIA: '/api/files/{0}/stream-media',
  FEATURE: '/api/features',

  DICTATION_SHADOWING_EXERCISES_ALL_PUBLISHED: '/api/exercises/allPublished',
  DICTATION_SHADOWING_EXERCISE_DETAILS: '/api/exercises/{0}/details',
  CHECK_DICTATION_ANSWER: '/api/exercisesubmissions/checkAnswerDictation',
  RESET_DICTATION_ANSWER: '/api/exercisesubmissions/resetAnswerDictation',

  OFFLINE_VIDEO: '/api/offlinevideos',
  OFFLINE_VIDEO_ID: '/api/offlinevideos/{0}',
  PERSONA: '/api/persona',
  PERSONA_ID: '/api/persona/{0}',
  USER_SUBSCRIPTIONS: '/api/dashboards/userSubscriptions',
  GET_ONE_PERMISSION_ID: '/api/permissions/getOne?userId={0}',
  PDF_FORM: '/api/pdfforms',
  PDF_FORM_ID: '/api/pdfforms/{0}',

  DOWNLOAD_OFFLINE_VIDEO: '/api/offlinevideos/downloadVideo?offlineVideoId={0}&cutStart={1}&cutEnd={2}',
  DOWNLOAD_OFFLINE_AUDIO: '/api/offlinevideos/downloadAudio?offlineVideoId={0}&cutStart={1}&cutEnd={2}',
  DOWNLOAD_AUDIO_YOUTUBE: '/api/videos/downloadAudio?url={0}&cutStart={1}&cutEnd={2}',
  DOWNLOAD_AUDIO_ID: '/api/audios/downloadCutAudio?audioId={0}&cutStart={1}&cutEnd={2}',
  DOWNLOAD_AUDIO: '/api/audios/downloadCutAudio?audioId={0}&cutStart={1}&cutEnd={2}',
  GET_MEDIA_FILE_NAME: '/api/inputs/{0}/mediaName',

  TRACKING: '/api/trackings',
  TRACKING_MANAGER: '/api/trackings/manager',
  STATISTIC_USERS: '/api/dashboards/statisticUsers',

  MODAL_AI: '/api/modalais',
  MODAL_AI_ID: '/api/modalais/{0}',

  SETTING: '/api/settings',
  SETTING_ID: '/api/settings/{0}',
  GET_TOOL_STUDENT: '/api/settings/getToolStudent',
  GET_VIDEO_TUTORIAL: '/api/settings/getVideoTutorial',
  GET_CHAT_BOT_INFO: '/api/settings/getChatbot',

  DOCUMENT_TEMPLATE: '/api/docxtemplates',
  DOCUMENT_TEMPLATE_ID: '/api/docxtemplates/{0}',
  PUBLISH_DOCUMENT_TEMPLATE: '/api/docxtemplates/publish',
  DOCUMENT_TEMPLATE_ORG_ID: '/api/docxtemplates/organization/{0}/getAll',
  PREVIEW_DOC_TEMPLATE: '/api/docxtemplates/{0}/preview',

  // preview file clickee base
  DOC_TEMPLATE_CREATE_PREVIEW_FILE_FOR_ORG: '/api/docxtemplates/createPreviewFile',
  // preview file org upload
  DOC_TEMPLATE_PREVIEW_TEMPLATE_FOR_ORG: '/api/docxtemplates/createPreviewTemplate',

  DOCUMENT_HEADER: '/api/customheader',
  DOCUMENT_HEADER_ID: '/api/customheader/{0}',

  UPLOAD_DOCUMENT_TEMPLATE: '/upload/docxTemplates',
  UPLOAD_ORG_DOC_TEMPLATE: '/upload/uploadOrgTemplate',

  DOCUMENT_OPTION: '/api/docxoptions',
  DOCUMENT_OPTION_ID: '/api/docxoptions/{0}',

  UPLOAD_ORG_AVATAR: '/upload/organizationAvatar',
  ACTIVITIES: '/api/activities',

  STATISTIC_ORG: '/api/organizations/statisticalOrganization',

  PROMPT_INSTRUCTION: '/api/instructions/{0}/prompt',

  API_KEY: '/api/apikeys',
  API_KEY_ID: '/api/apikeys/{0}',
  GET_EXAM_TEMPLATE: '/api/templates/getExamTemplate',
  GET_AVAILABLE_EXAM_TEMPLATE: '/api/templates/getAvailableExamTemplate',

  WHITELIST: '/api/whitelist',
  WHITELIST_MANAGER: '/api/whitelist/manager',
  WHITELIST_ID: '/api/whitelist/{0}',

  WAIT_LIST_MANAGER: '/api/waitlist/manager',
  WAIT_LIST_TO_WHITELIST: '/api/waitlist/{0}/whitelist',

  VOICE_OPTIONS: '/api/voices',
  VOICE_OPTION_DETAIL: '/api/voices/{0}',

  CREATE_WRITING_STUDENT: '/api/projects/createWritingFromTool',
  SUBMIT_WRITING_STUDENT: '/api/contents/submitIeltsWriting',

  UPDATE_USER_PERSONA: '/api/users/{0}/persona',
  RESEND_EMAIL_ACTIVATION: '/api/users/resendActiveAccountLink',
  SPEAKING_WORD_IPA: '/api/ipa/{0}/speak-word',

  FEEDBACK_GROUP: '/api/groupFeedBacks/listFeedback',
  GROUP_FEEDBACK: '/api/groupFeedbacks',
  FEEDBACK_CHECK_AUTO: '/api/groupFeedBacks/checkAutoFeedback',
  SUBMIT_FEEDBACK: '/api/userFeedBacks/submit',
  FEEDBACK_STATISTIC: '/api/dashboards/statisticFeedback',
  FEEDBACK_ANALYSIS: '/api/dashboards/analysisFeedback',

  DISCOUNT: '/api/discounts',
  DISCOUNT_ID: '/api/discounts/{0}',
  PROMOTION: '/api/promotions',
  PROMOTION_ID: '/api/promotions/{0}',
  PROMOTION_AVAILABLE: '/api/promotions/getAvailablePromotions',

  SUPPORT_BUSINESS: '/api/supportbusiness',
  SUPPORT_BUSINESS_ID: '/api/supportbusiness/{0}',

  DICTATION_AND_SHADOWING_UPLOAD_AUDIO: '/upload/exercisesAudio',
  DICTATION_AND_SHADOWING_EXERCISE: '/api/exercises',
  DICTATION_AND_SHADOWING_EXERCISE_ID: '/api/exercises/{0}',
  DICTATION_AND_SHADOWING_CREATE_AUDIO: '/api/exercises/textToSpeech',
  DICTATION_AND_SHADOWING_CREATE_EXERCISE: '/api/exercises/createExercise',
  DICTATION_AND_SHADOWING_UPDATE_EXERCISE: '/api/exercises/{0}/updateExercise',
  DICTATION_AND_SHADOWING_DELETE_EXERCISE: '/api/exercises/{0}/deleteExercise',

  SPEAKING_EXERCISE: '/api/spkexercises',
  SPEAKING_EXERCISE_ID: '/api/spkexercises/{0}',
  SPEAKING_CREATE_AUDIO: '/api/spkexercises/textToSpeech',
  SPEAKING_CREATE_QUESTION_AUDIO: '/api/spkexercises/textToSpeech',
  SPEAKING_CREATE_EXERCISE: '/api/spkexercises/createExercise',
  SPEAKING_UPDATE_EXERCISE: '/api/spkexercises/{0}/updateExercise',
  SPEAKING_DELETE_EXERCISE: '/api/spkexercises/{0}/deleteExercise',
  SPEAKING_UPLOAD_AUDIO: '/upload/speakingExercisesAudio',

  // Explain Service
  EXPLAIN: '/api/explain',
  EXPLAIN_ID: '/api/explain/{0}',
  EXPLAIN_COPY: '/api/explain/copy',
  EXPLAIN_TEST: '/api/explain/{0}/test',
  EXPLAIN_GENERATE_IDEA: '/api/explain/generateIdea',
  EXPLAIN_HELP_ME_UNDERSTAND: '/api/explain/helpMeUnderstand',
  EXPLAIN_HELP_ME_WRITE: '/api/explain/helpMeWrite',
  EXPLAIN_FIND_VOCABULARY: '/api/explain/findVocabulary',

  // E-commerce APIs
  // Products
  PRODUCTS: '/api/products',
  PRODUCT_DETAIL: '/api/products/{0}',
  PRODUCT_SEARCH: '/api/products/search',
  PRODUCT_BY_CATEGORY: '/api/products/category/{0}',
  PRODUCT_BESTSELLERS: '/api/products/bestsellers',
  PRODUCT_FEATURED: '/api/products/featured',
  PRODUCT_NEW: '/api/products/new',
  PRODUCT_RELATED: '/api/products/{0}/related',

  // Categories
  CATEGORIES: '/api/categories',
  CATEGORY_DETAIL: '/api/categories/{0}',

  // Carts
  CART_ADD: '/api/carts/add',
  CART_MY_CART: '/api/carts/my-cart',
  CART_UPDATE_QUANTITY: '/api/carts/{0}/quantity',
  CART_REMOVE_ITEM: '/api/carts/{0}',
  CART_CLEAR: '/api/carts/clear',

  // Orders
  ORDER_CREATE_FROM_CART: '/api/orders/create-from-cart',
  ORDER_MY_ORDERS: '/api/orders/my-orders',
  ORDER_DETAIL: '/api/orders/{0}/detail',
  ORDER_CANCEL: '/api/orders/{0}/cancel',
  ORDER_TRACKING: '/api/orders/{0}/tracking',

  // User Authentication
  USER_LOGIN: '/api/users/login',
  USER_REGISTER: '/api/users/register',
  USER_LOGOUT: '/api/users/logout',
  USER_ME: '/api/users/me',
  USER_FORGOT_PASSWORD: '/api/users/forgot-password',
  USER_RESET_PASSWORD: '/api/users/reset-password',

  // User Addresses
  USER_ADDRESSES: '/api/addresses',
  USER_ADDRESS_DETAIL: '/api/addresses/{0}',
  USER_ADDRESS_CREATE: '/api/addresses',
  USER_ADDRESS_UPDATE: '/api/addresses/{0}',
  USER_ADDRESS_DELETE: '/api/addresses/{0}',

  // Reviews
  PRODUCT_REVIEWS: '/api/reviews/product/{0}',
  REVIEW_CREATE: '/api/reviews',
  REVIEW_UPDATE: '/api/reviews/{0}',
  REVIEW_DELETE: '/api/reviews/{0}',

  // Coupons
  COUPON_CHECK: '/api/coupons/check',
  COUPON_APPLY: '/api/coupons/apply',

  // Payment
  PAYMENT_VNPAY: '/api/payments/vnpay',
  PAYMENT_CALLBACK: '/api/payments/callback',
};

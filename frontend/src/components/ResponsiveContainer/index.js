import React from 'react';
import { Grid } from 'antd';
import './ResponsiveContainer.scss';

const { useBreakpoint } = Grid;

const ResponsiveContainer = ({ 
  children, 
  className = '',
  maxWidth = '1200px',
  padding = true,
  center = true,
  fluid = false
}) => {
  const screens = useBreakpoint();

  const getResponsivePadding = () => {
    if (!padding) return '0';
    
    if (screens.xs) return '1rem 0.5rem';
    if (screens.sm) return '1.5rem 1rem';
    if (screens.md) return '2rem 1rem';
    return '2rem 1rem';
  };

  const containerStyle = {
    maxWidth: fluid ? '100%' : maxWidth,
    margin: center ? '0 auto' : '0',
    padding: getResponsivePadding(),
    width: '100%',
  };

  return (
    <div 
      className={`responsive-container ${className}`}
      style={containerStyle}
    >
      {children}
    </div>
  );
};

// Specific container variants
export const ShopContainer = ({ children, className = '' }) => (
  <ResponsiveContainer 
    className={`shop-container ${className}`}
    maxWidth="1200px"
  >
    {children}
  </ResponsiveContainer>
);

export const ContentContainer = ({ children, className = '' }) => (
  <ResponsiveContainer 
    className={`content-container ${className}`}
    maxWidth="800px"
  >
    {children}
  </ResponsiveContainer>
);

export const WideContainer = ({ children, className = '' }) => (
  <ResponsiveContainer 
    className={`wide-container ${className}`}
    maxWidth="1400px"
  >
    {children}
  </ResponsiveContainer>
);

export const FluidContainer = ({ children, className = '' }) => (
  <ResponsiveContainer 
    className={`fluid-container ${className}`}
    fluid={true}
  >
    {children}
  </ResponsiveContainer>
);

export default ResponsiveContainer;

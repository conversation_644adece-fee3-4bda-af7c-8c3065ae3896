.responsive-container {
  box-sizing: border-box;
  
  &.shop-container {
    // Shop-specific styles
  }
  
  &.content-container {
    // Content-specific styles
  }
  
  &.wide-container {
    // Wide container styles
  }
  
  &.fluid-container {
    // Fluid container styles
  }
}

// Responsive utilities
@media (max-width: 575px) {
  .responsive-container {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
}

@media (min-width: 576px) and (max-width: 767px) {
  .responsive-container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

@media (min-width: 768px) and (max-width: 991px) {
  .responsive-container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

@media (min-width: 992px) {
  .responsive-container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 2rem;

  .loading-text {
    color: #666;
    font-size: 14px;
  }
}

.loading-spinner-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

    .loading-text {
      color: #666;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

.loading-spinner-overlay {
  position: relative;

  .loading-overlay-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 10;

    .ant-typography {
      color: #666;
      font-weight: 500;
      margin: 0;
    }
  }
}

// Dark theme support
.dark-theme {
  .loading-spinner-fullscreen {
    background: rgba(0, 0, 0, 0.9);

    .loading-content {
      background: #1f1f1f;
      color: #fff;

      .loading-text {
        color: #ccc;
      }
    }
  }

  .loading-spinner-overlay {
    .loading-overlay-text {
      background: rgba(31, 31, 31, 0.95);
      color: #fff;

      .ant-typography {
        color: #ccc;
      }
    }
  }

  .loading-spinner {
    .loading-text {
      color: #ccc;
    }
  }
}

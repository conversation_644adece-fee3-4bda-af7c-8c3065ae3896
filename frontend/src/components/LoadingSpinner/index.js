import React from 'react';
import { Spin, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import './LoadingSpinner.scss';

const { Text } = Typography;

const LoadingSpinner = ({ 
  size = 'default', 
  text = '<PERSON><PERSON> tải...', 
  spinning = true,
  children,
  overlay = false,
  fullScreen = false,
  className = ''
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: getIconSize(size) }} spin />;

  function getIconSize(size) {
    switch (size) {
      case 'small': return 16;
      case 'large': return 32;
      case 'huge': return 48;
      default: return 24;
    }
  }

  if (fullScreen) {
    return (
      <div className={`loading-spinner-fullscreen ${className}`}>
        <div className="loading-content">
          <Spin indicator={antIcon} size={size} />
          {text && <Text className="loading-text">{text}</Text>}
        </div>
      </div>
    );
  }

  if (overlay) {
    return (
      <div className={`loading-spinner-overlay ${className}`}>
        <Spin 
          spinning={spinning} 
          indicator={antIcon}
          size={size}
        >
          {children}
        </Spin>
        {spinning && text && (
          <div className="loading-overlay-text">
            <Text>{text}</Text>
          </div>
        )}
      </div>
    );
  }

  if (children) {
    return (
      <Spin 
        spinning={spinning} 
        indicator={antIcon}
        size={size}
        className={className}
      >
        {children}
      </Spin>
    );
  }

  return (
    <div className={`loading-spinner ${className}`}>
      <Spin indicator={antIcon} size={size} />
      {text && <Text className="loading-text">{text}</Text>}
    </div>
  );
};

export default LoadingSpinner;

.lazy-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: #f5f5f5;

  .lazy-image-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    
    .ant-skeleton-image {
      width: 100% !important;
      height: 100% !important;
      
      .ant-skeleton-image-svg {
        width: 100%;
        height: 100%;
      }
    }
  }

  .lazy-image-img {
    display: block;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    &.fade-in {
      opacity: 1;
    }
  }

  &.loaded {
    .lazy-image-skeleton {
      display: none;
    }
  }

  &.error {
    .lazy-image-img {
      opacity: 0.7;
      filter: grayscale(100%);
    }
  }
}

// Responsive image utilities
.lazy-image-responsive {
  width: 100%;
  height: auto;
}

.lazy-image-cover {
  object-fit: cover;
}

.lazy-image-contain {
  object-fit: contain;
}

.lazy-image-fill {
  object-fit: fill;
}

// Animation variants
.lazy-image-blur {
  .lazy-image-img {
    filter: blur(5px);
    transition: filter 0.3s ease-in-out, opacity 0.3s ease-in-out;

    &.fade-in {
      filter: blur(0);
    }
  }
}

.lazy-image-scale {
  .lazy-image-img {
    transform: scale(1.1);
    transition: transform 0.3s ease-in-out, opacity 0.3s ease-in-out;

    &.fade-in {
      transform: scale(1);
    }
  }
}

// Dark theme
.dark-theme {
  .lazy-image {
    background-color: #262626;

    .lazy-image-skeleton {
      background-color: #262626;
    }
  }
}

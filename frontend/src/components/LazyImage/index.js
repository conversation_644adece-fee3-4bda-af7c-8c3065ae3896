import React from 'react';
import { Skeleton } from 'antd';
import { useLazyImage } from '@hooks/usePerformance';
import './LazyImage.scss';

const LazyImage = ({
  src,
  alt = '',
  placeholder = null,
  fallback = '/images/placeholder.jpg',
  className = '',
  style = {},
  width,
  height,
  objectFit = 'cover',
  loading = 'lazy',
  onLoad,
  onError,
  ...props
}) => {
  const { 
    setImageRef, 
    imageSrc, 
    loaded, 
    error, 
    handleLoad, 
    handleError 
  } = useLazyImage(src);

  const handleImageLoad = (e) => {
    handleLoad();
    if (onLoad) onLoad(e);
  };

  const handleImageError = (e) => {
    handleError();
    if (onError) onError(e);
  };

  const imageStyle = {
    width,
    height,
    objectFit,
    ...style,
  };

  const showSkeleton = !imageSrc || (!loaded && !error);
  const showImage = imageSrc && (loaded || error);
  const actualSrc = error ? fallback : imageSrc;

  return (
    <div 
      ref={setImageRef}
      className={`lazy-image ${className} ${loaded ? 'loaded' : ''} ${error ? 'error' : ''}`}
      style={{ width, height }}
    >
      {showSkeleton && (
        <div className="lazy-image-skeleton">
          {placeholder || (
            <Skeleton.Image 
              style={imageStyle}
              active
            />
          )}
        </div>
      )}
      
      {showImage && (
        <img
          src={actualSrc}
          alt={alt}
          style={imageStyle}
          loading={loading}
          onLoad={handleImageLoad}
          onError={handleImageError}
          className={`lazy-image-img ${loaded ? 'fade-in' : ''}`}
          {...props}
        />
      )}
    </div>
  );
};

export default LazyImage;

import React from 'react';
import { Result, Button, Typography } from 'antd';
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons';
import './ErrorBoundary.scss';

const { Paragraph, Text } = Typography;

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null 
    };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Log error to monitoring service
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // You can also log the error to an error reporting service
    // logErrorToService(error, errorInfo);
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      const { fallback, showDetails = false } = this.props;

      // Custom fallback UI
      if (fallback) {
        return fallback;
      }

      return (
        <div className="error-boundary">
          <Result
            status="500"
            title="Oops! Có lỗi xảy ra"
            subTitle="Xin lỗi, đã có lỗi không mong muốn xảy ra. Vui lòng thử lại sau."
            extra={[
              <Button 
                type="primary" 
                icon={<ReloadOutlined />}
                onClick={this.handleReload}
                key="reload"
              >
                Tải lại trang
              </Button>,
              <Button 
                icon={<HomeOutlined />}
                onClick={this.handleGoHome}
                key="home"
              >
                Về trang chủ
              </Button>
            ]}
          >
            {showDetails && this.state.error && (
              <div className="error-details">
                <Paragraph>
                  <Text strong>Chi tiết lỗi:</Text>
                </Paragraph>
                <Paragraph>
                  <Text code>{this.state.error.toString()}</Text>
                </Paragraph>
                {this.state.errorInfo && (
                  <details style={{ whiteSpace: 'pre-wrap', marginTop: 16 }}>
                    <summary>Stack trace</summary>
                    <Text code>{this.state.errorInfo.componentStack}</Text>
                  </details>
                )}
              </div>
            )}
          </Result>
        </div>
      );
    }

    return this.props.children;
  }
}

// Functional component wrapper for hooks
export const ErrorBoundaryWrapper = ({ children, ...props }) => {
  return (
    <ErrorBoundary {...props}>
      {children}
    </ErrorBoundary>
  );
};

export default ErrorBoundary;

.error-boundary {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;

  .ant-result {
    .ant-result-title {
      color: #ff4d4f;
    }

    .ant-result-subtitle {
      color: #666;
      margin-bottom: 2rem;
    }

    .ant-result-extra {
      .ant-btn {
        margin: 0 8px;
      }
    }
  }

  .error-details {
    text-align: left;
    max-width: 600px;
    margin-top: 2rem;
    padding: 1rem;
    background: #f5f5f5;
    border-radius: 8px;
    border-left: 4px solid #ff4d4f;

    .ant-typography {
      margin-bottom: 0.5rem;
    }

    details {
      margin-top: 1rem;
      
      summary {
        cursor: pointer;
        font-weight: 500;
        color: #666;
        margin-bottom: 0.5rem;
        
        &:hover {
          color: #1890ff;
        }
      }

      .ant-typography {
        background: #fff;
        padding: 1rem;
        border-radius: 4px;
        font-size: 12px;
        line-height: 1.4;
        max-height: 200px;
        overflow-y: auto;
      }
    }
  }
}

// Responsive
@media (max-width: 768px) {
  .error-boundary {
    padding: 1rem;

    .ant-result {
      .ant-result-title {
        font-size: 20px;
      }

      .ant-result-subtitle {
        font-size: 14px;
      }

      .ant-result-extra {
        .ant-btn {
          margin: 4px;
          width: 100%;
        }
      }
    }

    .error-details {
      padding: 0.75rem;
      font-size: 12px;

      details {
        .ant-typography {
          padding: 0.75rem;
          font-size: 11px;
        }
      }
    }
  }
}

// Dark theme
.dark-theme {
  .error-boundary {
    .error-details {
      background: #262626;
      border-left-color: #ff7875;

      details {
        .ant-typography {
          background: #1f1f1f;
          color: #ccc;
        }
      }
    }
  }
}

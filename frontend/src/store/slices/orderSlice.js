import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { orderService } from '../../services/api';

// Async thunks
export const createOrder = createAsyncThunk(
  'order/createOrder',
  async (orderData, { rejectWithValue }) => {
    try {
      const response = await orderService.createFromCart(orderData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Tạo đơn hàng thất bại');
    }
  }
);

export const getMyOrders = createAsyncThunk(
  'order/getMyOrders',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await orderService.getMyOrders(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải danh sách đơn hàng');
    }
  }
);

export const getOrderDetail = createAsyncThunk(
  'order/getOrderDetail',
  async (orderId, { rejectWithValue }) => {
    try {
      const response = await orderService.getOrderDetail(orderId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải thông tin đơn hàng');
    }
  }
);

export const cancelOrder = createAsyncThunk(
  'order/cancelOrder',
  async (orderId, { rejectWithValue }) => {
    try {
      const response = await orderService.cancelOrder(orderId);
      return { orderId, ...response };
    } catch (error) {
      return rejectWithValue(error.message || 'Hủy đơn hàng thất bại');
    }
  }
);

export const trackOrder = createAsyncThunk(
  'order/trackOrder',
  async (orderId, { rejectWithValue }) => {
    try {
      const response = await orderService.trackOrder(orderId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể theo dõi đơn hàng');
    }
  }
);

// Initial state
const initialState = {
  // Orders list
  orders: [],

  // Current order detail
  currentOrder: null,

  // Order tracking
  trackingInfo: null,

  // Pagination
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },

  // Filters
  filters: {
    status: '',
    dateFrom: null,
    dateTo: null,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },

  // Loading states
  loading: false,
  detailLoading: false,
  createLoading: false,
  cancelLoading: false,
  trackingLoading: false,

  // Error states
  error: null,
  detailError: null,
  createError: null,
  cancelError: null,
  trackingError: null,

  // Success states
  createSuccess: false,
  cancelSuccess: false,
};

// Order slice
const orderSlice = createSlice({
  name: 'order',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.detailError = null;
      state.createError = null;
      state.cancelError = null;
      state.trackingError = null;
    },
    clearSuccess: (state) => {
      state.createSuccess = false;
      state.cancelSuccess = false;
    },
    clearCurrentOrder: (state) => {
      state.currentOrder = null;
      state.trackingInfo = null;
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Create order
      .addCase(createOrder.pending, (state) => {
        state.createLoading = true;
        state.createError = null;
        state.createSuccess = false;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.createLoading = false;
        state.currentOrder = action.payload.order;
        state.createSuccess = true;
        state.createError = null;

        // Add new order to the beginning of orders list
        if (action.payload.order) {
          state.orders.unshift(action.payload.order);
        }
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.createLoading = false;
        state.createError = action.payload;
        state.createSuccess = false;
      })

      // Get my orders
      .addCase(getMyOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMyOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.orders = action.payload.orders || [];
        state.pagination = {
          page: action.payload.page || 1,
          limit: action.payload.limit || 10,
          total: action.payload.total || 0,
          totalPages: action.payload.totalPages || 0,
        };
        state.error = null;
      })
      .addCase(getMyOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get order detail
      .addCase(getOrderDetail.pending, (state) => {
        state.detailLoading = true;
        state.detailError = null;
      })
      .addCase(getOrderDetail.fulfilled, (state, action) => {
        state.detailLoading = false;
        state.currentOrder = action.payload.order;
        state.detailError = null;
      })
      .addCase(getOrderDetail.rejected, (state, action) => {
        state.detailLoading = false;
        state.detailError = action.payload;
      })

      // Cancel order
      .addCase(cancelOrder.pending, (state) => {
        state.cancelLoading = true;
        state.cancelError = null;
        state.cancelSuccess = false;
      })
      .addCase(cancelOrder.fulfilled, (state, action) => {
        state.cancelLoading = false;
        state.cancelSuccess = true;
        state.cancelError = null;

        // Update order status in orders list
        const orderIndex = state.orders.findIndex(order => order._id === action.payload.orderId);
        if (orderIndex !== -1) {
          state.orders[orderIndex].status = 'cancelled';
        }

        // Update current order if it's the same
        if (state.currentOrder && state.currentOrder._id === action.payload.orderId) {
          state.currentOrder.status = 'cancelled';
        }
      })
      .addCase(cancelOrder.rejected, (state, action) => {
        state.cancelLoading = false;
        state.cancelError = action.payload;
        state.cancelSuccess = false;
      })

      // Track order
      .addCase(trackOrder.pending, (state) => {
        state.trackingLoading = true;
        state.trackingError = null;
      })
      .addCase(trackOrder.fulfilled, (state, action) => {
        state.trackingLoading = false;
        state.trackingInfo = action.payload.tracking;
        state.trackingError = null;
      })
      .addCase(trackOrder.rejected, (state, action) => {
        state.trackingLoading = false;
        state.trackingError = action.payload;
      });
  },
});

// Export actions
export const {
  clearError,
  clearSuccess,
  clearCurrentOrder,
  setFilters,
  clearFilters,
  setPagination,
} = orderSlice.actions;

// Export selectors
export const selectOrder = (state) => state.order;
export const selectOrders = (state) => state.order.orders;
export const selectCurrentOrder = (state) => state.order.currentOrder;
export const selectTrackingInfo = (state) => state.order.trackingInfo;
export const selectOrderPagination = (state) => state.order.pagination;
export const selectOrderFilters = (state) => state.order.filters;
export const selectOrderLoading = (state) => state.order.loading;
export const selectOrderError = (state) => state.order.error;
export const selectCreateOrderLoading = (state) => state.order.createLoading;
export const selectCreateOrderSuccess = (state) => state.order.createSuccess;

// Export reducer
export default orderSlice.reducer;

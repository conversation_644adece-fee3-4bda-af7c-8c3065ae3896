import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { categoryService } from '../../services/api';

// Async thunks
export const getCategories = createAsyncThunk(
  'category/getCategories',
  async (_, { rejectWithValue }) => {
    try {
      const response = await categoryService.getCategories();
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải danh mục');
    }
  }
);

export const getCategoryDetail = createAsyncThunk(
  'category/getCategoryDetail',
  async (categoryId, { rejectWithValue }) => {
    try {
      const response = await categoryService.getCategoryDetail(categoryId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải thông tin danh mục');
    }
  }
);

// Initial state
const initialState = {
  categories: [],
  currentCategory: null,
  loading: false,
  detailLoading: false,
  error: null,
  detailError: null,
};

// Category slice
const categorySlice = createSlice({
  name: 'category',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.detailError = null;
    },
    clearCurrentCategory: (state) => {
      state.currentCategory = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get categories
      .addCase(getCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload.categories || [];
        state.error = null;
      })
      .addCase(getCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get category detail
      .addCase(getCategoryDetail.pending, (state) => {
        state.detailLoading = true;
        state.detailError = null;
      })
      .addCase(getCategoryDetail.fulfilled, (state, action) => {
        state.detailLoading = false;
        state.currentCategory = action.payload.category;
        state.detailError = null;
      })
      .addCase(getCategoryDetail.rejected, (state, action) => {
        state.detailLoading = false;
        state.detailError = action.payload;
      });
  },
});

// Export actions
export const { clearError, clearCurrentCategory } = categorySlice.actions;

// Export selectors
export const selectCategory = (state) => state.category;
export const selectCategories = (state) => state.category.categories;
export const selectCurrentCategory = (state) => state.category.currentCategory;
export const selectCategoryLoading = (state) => state.category.loading;
export const selectCategoryError = (state) => state.category.error;

// Export reducer
export default categorySlice.reducer;

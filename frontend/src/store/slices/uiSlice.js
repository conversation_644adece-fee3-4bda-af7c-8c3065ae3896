import { createSlice } from '@reduxjs/toolkit';

// Initial state
const initialState = {
  // Global loading
  globalLoading: false,
  
  // Sidebar
  sidebarCollapsed: false,
  
  // Mobile menu
  mobileMenuVisible: false,
  
  // Search
  searchVisible: false,
  searchQuery: '',
  
  // Cart drawer
  cartDrawerVisible: false,
  
  // Modals
  loginModalVisible: false,
  registerModalVisible: false,
  
  // Notifications
  notifications: [],
  
  // Theme
  theme: 'light',
  
  // Language
  language: 'vi',
  
  // Page settings
  pageTitle: '',
  breadcrumbs: [],
  
  // Filters drawer (mobile)
  filtersDrawerVisible: false,
  
  // Quick view modal
  quickViewVisible: false,
  quickViewProduct: null,
};

// UI slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Global loading
    setGlobalLoading: (state, action) => {
      state.globalLoading = action.payload;
    },
    
    // Sidebar
    toggleSidebar: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },
    setSidebarCollapsed: (state, action) => {
      state.sidebarCollapsed = action.payload;
    },
    
    // Mobile menu
    toggleMobileMenu: (state) => {
      state.mobileMenuVisible = !state.mobileMenuVisible;
    },
    setMobileMenuVisible: (state, action) => {
      state.mobileMenuVisible = action.payload;
    },
    
    // Search
    toggleSearch: (state) => {
      state.searchVisible = !state.searchVisible;
    },
    setSearchVisible: (state, action) => {
      state.searchVisible = action.payload;
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    
    // Cart drawer
    toggleCartDrawer: (state) => {
      state.cartDrawerVisible = !state.cartDrawerVisible;
    },
    setCartDrawerVisible: (state, action) => {
      state.cartDrawerVisible = action.payload;
    },
    
    // Auth modals
    setLoginModalVisible: (state, action) => {
      state.loginModalVisible = action.payload;
    },
    setRegisterModalVisible: (state, action) => {
      state.registerModalVisible = action.payload;
    },
    
    // Notifications
    addNotification: (state, action) => {
      const notification = {
        id: Date.now(),
        type: 'info',
        duration: 4.5,
        ...action.payload,
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action) => {
      const id = action.payload;
      state.notifications = state.notifications.filter(notification => notification.id !== id);
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // Theme
    setTheme: (state, action) => {
      state.theme = action.payload;
    },
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    
    // Language
    setLanguage: (state, action) => {
      state.language = action.payload;
    },
    
    // Page settings
    setPageTitle: (state, action) => {
      state.pageTitle = action.payload;
    },
    setBreadcrumbs: (state, action) => {
      state.breadcrumbs = action.payload;
    },
    
    // Filters drawer
    toggleFiltersDrawer: (state) => {
      state.filtersDrawerVisible = !state.filtersDrawerVisible;
    },
    setFiltersDrawerVisible: (state, action) => {
      state.filtersDrawerVisible = action.payload;
    },
    
    // Quick view
    setQuickViewVisible: (state, action) => {
      state.quickViewVisible = action.payload;
    },
    setQuickViewProduct: (state, action) => {
      state.quickViewProduct = action.payload;
    },
    openQuickView: (state, action) => {
      state.quickViewProduct = action.payload;
      state.quickViewVisible = true;
    },
    closeQuickView: (state) => {
      state.quickViewVisible = false;
      state.quickViewProduct = null;
    },
    
    // Reset UI state
    resetUI: (state) => {
      return {
        ...initialState,
        theme: state.theme,
        language: state.language,
      };
    },
  },
});

// Export actions
export const {
  setGlobalLoading,
  toggleSidebar,
  setSidebarCollapsed,
  toggleMobileMenu,
  setMobileMenuVisible,
  toggleSearch,
  setSearchVisible,
  setSearchQuery,
  toggleCartDrawer,
  setCartDrawerVisible,
  setLoginModalVisible,
  setRegisterModalVisible,
  addNotification,
  removeNotification,
  clearNotifications,
  setTheme,
  toggleTheme,
  setLanguage,
  setPageTitle,
  setBreadcrumbs,
  toggleFiltersDrawer,
  setFiltersDrawerVisible,
  setQuickViewVisible,
  setQuickViewProduct,
  openQuickView,
  closeQuickView,
  resetUI,
} = uiSlice.actions;

// Export selectors
export const selectUI = (state) => state.ui;
export const selectGlobalLoading = (state) => state.ui.globalLoading;
export const selectSidebarCollapsed = (state) => state.ui.sidebarCollapsed;
export const selectMobileMenuVisible = (state) => state.ui.mobileMenuVisible;
export const selectSearchVisible = (state) => state.ui.searchVisible;
export const selectSearchQuery = (state) => state.ui.searchQuery;
export const selectCartDrawerVisible = (state) => state.ui.cartDrawerVisible;
export const selectLoginModalVisible = (state) => state.ui.loginModalVisible;
export const selectRegisterModalVisible = (state) => state.ui.registerModalVisible;
export const selectNotifications = (state) => state.ui.notifications;
export const selectTheme = (state) => state.ui.theme;
export const selectLanguage = (state) => state.ui.language;
export const selectPageTitle = (state) => state.ui.pageTitle;
export const selectBreadcrumbs = (state) => state.ui.breadcrumbs;
export const selectFiltersDrawerVisible = (state) => state.ui.filtersDrawerVisible;
export const selectQuickViewVisible = (state) => state.ui.quickViewVisible;
export const selectQuickViewProduct = (state) => state.ui.quickViewProduct;

// Export reducer
export default uiSlice.reducer;

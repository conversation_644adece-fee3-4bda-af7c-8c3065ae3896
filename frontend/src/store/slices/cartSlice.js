import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { cartService } from '../../services/api';

// Async thunks
export const addToCart = createAsyncThunk(
  'cart/addToCart',
  async (productData, { rejectWithValue }) => {
    try {
      const response = await cartService.addToCart(productData);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Thêm vào giỏ hàng thất bại');
    }
  }
);

export const getCart = createAsyncThunk(
  'cart/getCart',
  async (_, { rejectWithValue }) => {
    try {
      const response = await cartService.getMyCart();
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải giỏ hàng');
    }
  }
);

export const updateCartItemQuantity = createAsyncThunk(
  'cart/updateQuantity',
  async ({ cartItemId, quantity }, { rejectWithValue }) => {
    try {
      const response = await cartService.updateQuantity(cartItemId, quantity);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Cập nhật số lượng thất bại');
    }
  }
);

export const removeCartItem = createAsyncThunk(
  'cart/removeItem',
  async (cartItemId, { rejectWithValue }) => {
    try {
      await cartService.removeItem(cartItemId);
      return cartItemId;
    } catch (error) {
      return rejectWithValue(error.message || 'Xóa sản phẩm thất bại');
    }
  }
);

export const clearCart = createAsyncThunk(
  'cart/clearCart',
  async (_, { rejectWithValue }) => {
    try {
      await cartService.clearCart();
      return true;
    } catch (error) {
      return rejectWithValue(error.message || 'Xóa giỏ hàng thất bại');
    }
  }
);

// Initial state
const initialState = {
  items: [],
  totalItems: 0,
  totalAmount: 0,
  loading: false,
  error: null,
  addingToCart: false,
  updatingQuantity: false,
  removingItem: false,
  clearing: false,
};

// Helper functions
const calculateTotals = (items) => {
  const totalItems = items.reduce((total, item) => total + item.quantity, 0);
  const totalAmount = items.reduce((total, item) => total + item.itemTotal, 0);
  return { totalItems, totalAmount };
};

// Cart slice
const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCartLocal: (state) => {
      state.items = [];
      state.totalItems = 0;
      state.totalAmount = 0;
    },
    // Local cart operations (for offline use)
    addToCartLocal: (state, action) => {
      const { variantId, quantity = 1 } = action.payload;
      const existingItem = state.items.find(item => item.variantId._id === variantId._id);

      if (existingItem) {
        existingItem.quantity += quantity;
        existingItem.itemTotal = existingItem.variantId.price * existingItem.quantity;
      } else {
        const newItem = {
          _id: Date.now().toString(),
          variantId,
          quantity,
          itemTotal: variantId.price * quantity,
        };
        state.items.push(newItem);
      }

      const totals = calculateTotals(state.items);
      state.totalItems = totals.totalItems;
      state.totalAmount = totals.totalAmount;
    },
    updateQuantityLocal: (state, action) => {
      const { itemId, quantity } = action.payload;
      const item = state.items.find(item => item._id === itemId);

      if (item && quantity > 0) {
        item.quantity = quantity;
        item.itemTotal = item.variantId.price * quantity;

        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalAmount = totals.totalAmount;
      }
    },
    removeItemLocal: (state, action) => {
      const itemId = action.payload;
      state.items = state.items.filter(item => item._id !== itemId);

      const totals = calculateTotals(state.items);
      state.totalItems = totals.totalItems;
      state.totalAmount = totals.totalAmount;
    },
  },
  extraReducers: (builder) => {
    builder
      // Add to cart
      .addCase(addToCart.pending, (state) => {
        state.addingToCart = true;
        state.error = null;
      })
      .addCase(addToCart.fulfilled, (state, action) => {
        state.addingToCart = false;
        state.items = action.payload.items || [];
        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalAmount = totals.totalAmount;
        state.error = null;
      })
      .addCase(addToCart.rejected, (state, action) => {
        state.addingToCart = false;
        state.error = action.payload;
      })

      // Get cart
      .addCase(getCart.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCart.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload.items || [];
        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalAmount = totals.totalAmount;
        state.error = null;
      })
      .addCase(getCart.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Update quantity
      .addCase(updateCartItemQuantity.pending, (state) => {
        state.updatingQuantity = true;
        state.error = null;
      })
      .addCase(updateCartItemQuantity.fulfilled, (state, action) => {
        state.updatingQuantity = false;
        state.items = action.payload.items || [];
        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalAmount = totals.totalAmount;
        state.error = null;
      })
      .addCase(updateCartItemQuantity.rejected, (state, action) => {
        state.updatingQuantity = false;
        state.error = action.payload;
      })

      // Remove item
      .addCase(removeCartItem.pending, (state) => {
        state.removingItem = true;
        state.error = null;
      })
      .addCase(removeCartItem.fulfilled, (state, action) => {
        state.removingItem = false;
        state.items = state.items.filter(item => item._id !== action.payload);
        const totals = calculateTotals(state.items);
        state.totalItems = totals.totalItems;
        state.totalAmount = totals.totalAmount;
        state.error = null;
      })
      .addCase(removeCartItem.rejected, (state, action) => {
        state.removingItem = false;
        state.error = action.payload;
      })

      // Clear cart
      .addCase(clearCart.pending, (state) => {
        state.clearing = true;
        state.error = null;
      })
      .addCase(clearCart.fulfilled, (state) => {
        state.clearing = false;
        state.items = [];
        state.totalItems = 0;
        state.totalAmount = 0;
        state.error = null;
      })
      .addCase(clearCart.rejected, (state, action) => {
        state.clearing = false;
        state.error = action.payload;
      });
  },
});

// Export actions
export const {
  clearError,
  clearCartLocal,
  addToCartLocal,
  updateQuantityLocal,
  removeItemLocal
} = cartSlice.actions;

// Export selectors
export const selectCart = (state) => state.cart;
export const selectCartItems = (state) => state.cart.items;
export const selectCartTotalItems = (state) => state.cart.totalItems;
export const selectCartTotalAmount = (state) => state.cart.totalAmount;
export const selectCartLoading = (state) => state.cart.loading;
export const selectCartError = (state) => state.cart.error;

// Export reducer
export default cartSlice.reducer;

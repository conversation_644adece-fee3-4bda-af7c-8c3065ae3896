import { createSlice } from '@reduxjs/toolkit';

// Initial state
const initialState = {
  items: [],
  totalItems: 0,
};

// Wishlist slice
const wishlistSlice = createSlice({
  name: 'wishlist',
  initialState,
  reducers: {
    addToWishlist: (state, action) => {
      const product = action.payload;
      const existingItem = state.items.find(item => item._id === product._id);
      
      if (!existingItem) {
        state.items.push({
          ...product,
          addedAt: new Date().toISOString(),
        });
        state.totalItems = state.items.length;
      }
    },
    removeFromWishlist: (state, action) => {
      const productId = action.payload;
      state.items = state.items.filter(item => item._id !== productId);
      state.totalItems = state.items.length;
    },
    toggleWishlist: (state, action) => {
      const product = action.payload;
      const existingIndex = state.items.findIndex(item => item._id === product._id);
      
      if (existingIndex !== -1) {
        // Remove from wishlist
        state.items.splice(existingIndex, 1);
      } else {
        // Add to wishlist
        state.items.push({
          ...product,
          addedAt: new Date().toISOString(),
        });
      }
      state.totalItems = state.items.length;
    },
    clearWishlist: (state) => {
      state.items = [];
      state.totalItems = 0;
    },
    updateWishlistItem: (state, action) => {
      const { productId, updates } = action.payload;
      const itemIndex = state.items.findIndex(item => item._id === productId);
      
      if (itemIndex !== -1) {
        state.items[itemIndex] = { ...state.items[itemIndex], ...updates };
      }
    },
  },
});

// Export actions
export const {
  addToWishlist,
  removeFromWishlist,
  toggleWishlist,
  clearWishlist,
  updateWishlistItem,
} = wishlistSlice.actions;

// Export selectors
export const selectWishlist = (state) => state.wishlist;
export const selectWishlistItems = (state) => state.wishlist.items;
export const selectWishlistTotalItems = (state) => state.wishlist.totalItems;
export const selectIsInWishlist = (productId) => (state) => 
  state.wishlist.items.some(item => item._id === productId);

// Export reducer
export default wishlistSlice.reducer;

import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { productService } from '@src/services/api';

// Async thunks
export const getProducts = createAsyncThunk(
  'product/getProducts',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await productService.getProducts(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải danh sách sản phẩm');
    }
  }
);

export const getProductDetail = createAsyncThunk(
  'product/getProductDetail',
  async (productId, { rejectWithValue }) => {
    try {
      const response = await productService.getProductDetail(productId);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải thông tin sản phẩm');
    }
  }
);

export const searchProducts = createAsyncThunk(
  'product/searchProducts',
  async ({ query, params = {} }, { rejectWithValue }) => {
    try {
      const response = await productService.searchProducts(query, params);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Tìm kiếm thất bại');
    }
  }
);

export const getFeaturedProducts = createAsyncThunk(
  'product/getFeaturedProducts',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await productService.getFeaturedProducts(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải sản phẩm nổi bật');
    }
  }
);

export const getNewProducts = createAsyncThunk(
  'product/getNewProducts',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await productService.getNewProducts(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải sản phẩm mới');
    }
  }
);

export const getBestsellers = createAsyncThunk(
  'product/getBestsellers',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await productService.getBestsellers(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải sản phẩm bán chạy');
    }
  }
);

export const getRelatedProducts = createAsyncThunk(
  'product/getRelatedProducts',
  async ({ productId, params = {} }, { rejectWithValue }) => {
    try {
      const response = await productService.getRelatedProducts(productId, params);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải sản phẩm liên quan');
    }
  }
);

export const getProductsByCategory = createAsyncThunk(
  'product/getProductsByCategory',
  async ({ categoryId, params = {} }, { rejectWithValue }) => {
    try {
      const response = await productService.getProductsByCategory(categoryId, params);
      return response;
    } catch (error) {
      return rejectWithValue(error.message || 'Không thể tải sản phẩm theo danh mục');
    }
  }
);

// Initial state
const initialState = {
  // Product lists
  products: [],
  featuredProducts: [],
  newProducts: [],
  bestsellerProducts: [],
  relatedProducts: [],
  searchResults: [],

  // Current product detail
  currentProduct: null,

  // Pagination
  pagination: {
    page: 1,
    limit: 12,
    total: 0,
    totalPages: 0,
  },

  // Filters
  filters: {
    categoryIds: [],
    priceRange: [0, 100000000],
    rating: 0,
    inStock: false,
    isFeatured: false,
    isNewProduct: false,
    sortBy: 'createdAt',
    sortOrder: 'desc',
  },

  // Search
  searchQuery: '',
  searchSuggestions: [],

  // Loading states
  loading: false,
  detailLoading: false,
  featuredLoading: false,
  newLoading: false,
  bestsellerLoading: false,
  relatedLoading: false,
  searchLoading: false,

  // Error states
  error: null,
  detailError: null,
  featuredError: null,
  newError: null,
  bestsellerError: null,
  relatedError: null,
  searchError: null,
};

// Product slice
const productSlice = createSlice({
  name: 'product',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
      state.detailError = null;
      state.featuredError = null;
      state.newError = null;
      state.bestsellerError = null;
      state.relatedError = null;
      state.searchError = null;
    },
    clearCurrentProduct: (state) => {
      state.currentProduct = null;
      state.relatedProducts = [];
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
      state.searchQuery = '';
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Get products
      .addCase(getProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.products || [];
        state.pagination = {
          page: action.payload.page || 1,
          limit: action.payload.limit || 12,
          total: action.payload.total || 0,
          totalPages: action.payload.totalPages || 0,
        };
        state.error = null;
      })
      .addCase(getProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })

      // Get product detail
      .addCase(getProductDetail.pending, (state) => {
        state.detailLoading = true;
        state.detailError = null;
      })
      .addCase(getProductDetail.fulfilled, (state, action) => {
        state.detailLoading = false;
        state.currentProduct = action.payload.product;
        state.detailError = null;
      })
      .addCase(getProductDetail.rejected, (state, action) => {
        state.detailLoading = false;
        state.detailError = action.payload;
      })

      // Search products
      .addCase(searchProducts.pending, (state) => {
        state.searchLoading = true;
        state.searchError = null;
      })
      .addCase(searchProducts.fulfilled, (state, action) => {
        state.searchLoading = false;
        state.searchResults = action.payload.products || [];
        state.pagination = {
          page: action.payload.page || 1,
          limit: action.payload.limit || 12,
          total: action.payload.total || 0,
          totalPages: action.payload.totalPages || 0,
        };
        state.searchError = null;
      })
      .addCase(searchProducts.rejected, (state, action) => {
        state.searchLoading = false;
        state.searchError = action.payload;
      })

      // Get featured products
      .addCase(getFeaturedProducts.pending, (state) => {
        state.featuredLoading = true;
        state.featuredError = null;
      })
      .addCase(getFeaturedProducts.fulfilled, (state, action) => {
        state.featuredLoading = false;
        state.featuredProducts = action.payload.products || [];
        state.featuredError = null;
      })
      .addCase(getFeaturedProducts.rejected, (state, action) => {
        state.featuredLoading = false;
        state.featuredError = action.payload;
      })

      // Get new products
      .addCase(getNewProducts.pending, (state) => {
        state.newLoading = true;
        state.newError = null;
      })
      .addCase(getNewProducts.fulfilled, (state, action) => {
        state.newLoading = false;
        state.newProducts = action.payload.products || [];
        state.newError = null;
      })
      .addCase(getNewProducts.rejected, (state, action) => {
        state.newLoading = false;
        state.newError = action.payload;
      })

      // Get bestsellers
      .addCase(getBestsellers.pending, (state) => {
        state.bestsellerLoading = true;
        state.bestsellerError = null;
      })
      .addCase(getBestsellers.fulfilled, (state, action) => {
        state.bestsellerLoading = false;
        state.bestsellerProducts = action.payload.products || [];
        state.bestsellerError = null;
      })
      .addCase(getBestsellers.rejected, (state, action) => {
        state.bestsellerLoading = false;
        state.bestsellerError = action.payload;
      })

      // Get related products
      .addCase(getRelatedProducts.pending, (state) => {
        state.relatedLoading = true;
        state.relatedError = null;
      })
      .addCase(getRelatedProducts.fulfilled, (state, action) => {
        state.relatedLoading = false;
        state.relatedProducts = action.payload.products || [];
        state.relatedError = null;
      })
      .addCase(getRelatedProducts.rejected, (state, action) => {
        state.relatedLoading = false;
        state.relatedError = action.payload;
      })

      // Get products by category
      .addCase(getProductsByCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProductsByCategory.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload.products || [];
        state.pagination = {
          page: action.payload.page || 1,
          limit: action.payload.limit || 12,
          total: action.payload.total || 0,
          totalPages: action.payload.totalPages || 0,
        };
        state.error = null;
      })
      .addCase(getProductsByCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

// Export actions
export const {
  clearError,
  clearCurrentProduct,
  setFilters,
  clearFilters,
  setSearchQuery,
  clearSearchResults,
  setPagination,
} = productSlice.actions;

// Export selectors
export const selectProduct = (state) => state.product;
export const selectProducts = (state) => state.product.products;
export const selectCurrentProduct = (state) => state.product.currentProduct;
export const selectFeaturedProducts = (state) => state.product.featuredProducts;
export const selectNewProducts = (state) => state.product.newProducts;
export const selectBestsellerProducts = (state) => state.product.bestsellerProducts;
export const selectRelatedProducts = (state) => state.product.relatedProducts;
export const selectSearchResults = (state) => state.product.searchResults;
export const selectProductPagination = (state) => state.product.pagination;
export const selectProductFilters = (state) => state.product.filters;
export const selectProductLoading = (state) => state.product.loading;
export const selectProductError = (state) => state.product.error;

// Export reducer
export default productSlice.reducer;

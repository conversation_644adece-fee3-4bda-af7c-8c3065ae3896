import axios from 'axios';
import { API } from '@api';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('accessToken');
      localStorage.removeItem('user');
      window.location.href = '/auth';
    }
    return Promise.reject(error.response?.data || error.message);
  }
);

// Helper function to format URL with parameters
const formatUrl = (url, ...params) => {
  return params.reduce((formattedUrl, param, index) => {
    return formattedUrl.replace(`{${index}}`, param);
  }, url);
};

// Auth Services
export const authService = {
  login: (credentials) => apiClient.post(API.USER_LOGIN, credentials),
  register: (userData) => apiClient.post(API.USER_REGISTER, userData),
  logout: () => apiClient.post(API.USER_LOGOUT),
  getMe: () => apiClient.get(API.USER_ME),
  forgotPassword: (email) => apiClient.post(API.USER_FORGOT_PASSWORD, { email }),
  resetPassword: (token, password) => apiClient.post(API.USER_RESET_PASSWORD, { token, password }),
  refreshToken: () => apiClient.post(API.USER_REFRESH_TOKEN),
};

// Product Services
export const productService = {
  getProducts: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiClient.get(`${API.PRODUCTS}?${queryString}`);
  },
  getProductDetail: (productId) => apiClient.get(formatUrl(API.PRODUCT_DETAIL, productId)),
  searchProducts: (query, params = {}) => {
    const queryString = new URLSearchParams({ q: query, ...params }).toString();
    return apiClient.get(`${API.PRODUCT_SEARCH}?${queryString}`);
  },
  getProductsByCategory: (categoryId, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiClient.get(`${formatUrl(API.PRODUCT_BY_CATEGORY, categoryId)}?${queryString}`);
  },
  getBestsellers: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiClient.get(`${API.PRODUCT_BESTSELLERS}?${queryString}`);
  },
  getFeaturedProducts: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiClient.get(`${API.PRODUCT_FEATURED}?${queryString}`);
  },
  getNewProducts: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiClient.get(`${API.PRODUCT_NEW}?${queryString}`);
  },
  getRelatedProducts: (productId, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiClient.get(`${formatUrl(API.PRODUCT_RELATED, productId)}?${queryString}`);
  },
};

// Category Services
export const categoryService = {
  getCategories: () => apiClient.get(API.CATEGORIES),
  getCategoryDetail: (categoryId) => apiClient.get(formatUrl(API.CATEGORY_DETAIL, categoryId)),
};

// Cart Services
export const cartService = {
  addToCart: (productData) => apiClient.post(API.CART_ADD, productData),
  getMyCart: () => apiClient.get(API.CART_MY_CART),
  updateQuantity: (cartItemId, quantity) =>
    apiClient.put(formatUrl(API.CART_UPDATE_QUANTITY, cartItemId), { quantity }),
  removeItem: (cartItemId) => apiClient.delete(formatUrl(API.CART_REMOVE_ITEM, cartItemId)),
  clearCart: () => apiClient.delete(API.CART_CLEAR),
};

// Order Services
export const orderService = {
  createFromCart: (orderData) => apiClient.post(API.ORDER_CREATE_FROM_CART, orderData),
  getMyOrders: (params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiClient.get(`${API.ORDER_MY_ORDERS}?${queryString}`);
  },
  getOrderDetail: (orderId) => apiClient.get(formatUrl(API.ORDER_DETAIL, orderId)),
  cancelOrder: (orderId) => apiClient.put(formatUrl(API.ORDER_CANCEL, orderId)),
  trackOrder: (orderId) => apiClient.get(formatUrl(API.ORDER_TRACKING, orderId)),
};

// Address Services
export const addressService = {
  getAddresses: () => apiClient.get(API.USER_ADDRESSES),
  getAddressDetail: (addressId) => apiClient.get(formatUrl(API.USER_ADDRESS_DETAIL, addressId)),
  createAddress: (addressData) => apiClient.post(API.USER_ADDRESS_CREATE, addressData),
  updateAddress: (addressId, addressData) =>
    apiClient.put(formatUrl(API.USER_ADDRESS_UPDATE, addressId), addressData),
  deleteAddress: (addressId) => apiClient.delete(formatUrl(API.USER_ADDRESS_DELETE, addressId)),
};

// Review Services
export const reviewService = {
  getProductReviews: (productId, params = {}) => {
    const queryString = new URLSearchParams(params).toString();
    return apiClient.get(`${formatUrl(API.PRODUCT_REVIEWS, productId)}?${queryString}`);
  },
  createReview: (reviewData) => apiClient.post(API.REVIEW_CREATE, reviewData),
  updateReview: (reviewId, reviewData) =>
    apiClient.put(formatUrl(API.REVIEW_UPDATE, reviewId), reviewData),
  deleteReview: (reviewId) => apiClient.delete(formatUrl(API.REVIEW_DELETE, reviewId)),
};

// Coupon Services
export const couponService = {
  checkCoupon: (couponCode) => apiClient.post(API.COUPON_CHECK, { code: couponCode }),
  applyCoupon: (couponCode, cartTotal) =>
    apiClient.post(API.COUPON_APPLY, { code: couponCode, cartTotal }),
};

// Payment Services
export const paymentService = {
  createVNPayPayment: (paymentData) => apiClient.post(API.PAYMENT_VNPAY, paymentData),
  handlePaymentCallback: (callbackData) => apiClient.post(API.PAYMENT_CALLBACK, callbackData),
};

// User Profile Services
export const userService = {
  updateProfile: (userData) => apiClient.put(API.USER_ME, userData),
  changePassword: (passwordData) => apiClient.put(API.USER_CHANGE_PASSWORD, passwordData),
  uploadAvatar: (formData) => {
    return apiClient.post(API.UPLOAD_IMAGE, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

// File Upload Services
export const uploadService = {
  uploadImage: (file) => {
    const formData = new FormData();
    formData.append('image', file);
    return apiClient.post(API.UPLOAD_IMAGE, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  uploadFile: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return apiClient.post(API.UPLOAD_FILE, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
};

// Export the axios instance for custom requests
export { apiClient };

// Export all services as default
export default {
  auth: authService,
  product: productService,
  category: categoryService,
  cart: cartService,
  order: orderService,
  address: addressService,
  review: reviewService,
  coupon: couponService,
  payment: paymentService,
  user: userService,
  upload: uploadService,
};

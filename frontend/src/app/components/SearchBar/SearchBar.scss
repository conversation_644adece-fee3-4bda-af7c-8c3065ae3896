.search-bar {
  width: 100%;
  max-width: 600px;
  
  .search-autocomplete {
    width: 100%;
    
    .search-input {
      border-radius: 8px;
      
      .ant-input {
        border-radius: 8px;
      }
      
      &:hover,
      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}

.search-dropdown {
  .suggestion-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 0;
    cursor: pointer;
    
    .ant-image {
      border-radius: 4px;
      overflow: hidden;
      flex-shrink: 0;
    }
    
    .suggestion-content {
      display: flex;
      flex-direction: column;
      gap: 2px;
      flex: 1;
      min-width: 0;
      
      .ant-typography {
        margin: 0;
        
        &:first-child {
          font-size: 14px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        &:last-child {
          font-size: 12px;
          color: #ff4d4f;
        }
      }
    }
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
  
  .ant-select-item-option-content {
    padding: 0;
  }
}

@media (max-width: 768px) {
  .search-bar {
    max-width: 100%;
    
    .search-autocomplete {
      .search-input {
        .ant-input {
          font-size: 16px; // Prevent zoom on iOS
        }
      }
    }
  }
  
  .search-dropdown {
    .suggestion-item {
      gap: 8px;
      
      .ant-image {
        width: 32px !important;
        height: 32px !important;
      }
      
      .suggestion-content {
        .ant-typography {
          &:first-child {
            font-size: 13px;
          }
          
          &:last-child {
            font-size: 11px;
          }
        }
      }
    }
  }
}

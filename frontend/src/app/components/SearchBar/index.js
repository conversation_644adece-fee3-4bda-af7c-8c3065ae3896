import React, { useState, useEffect } from 'react';
import { Input, AutoComplete, Typography, Space, Image } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useDebounce } from '@app/hooks/useDebounce';
import { LINK } from '@link';
import './SearchBar.scss';

const { Text } = Typography;

const SearchBar = ({ 
  onSearch, 
  placeholder = "Tìm kiếm sản phẩm...", 
  showSuggestions = true,
  size = "large"
}) => {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  
  const debouncedSearchValue = useDebounce(searchValue, 300);

  useEffect(() => {
    if (debouncedSearchValue && showSuggestions) {
      fetchSuggestions(debouncedSearchValue);
    } else {
      setSuggestions([]);
    }
  }, [debouncedSearchValue, showSuggestions]);

  const fetchSuggestions = async (query) => {
    if (!query.trim()) return;
    
    setLoading(true);
    try {
      // Mock API call - replace with actual API
      const mockSuggestions = [
        {
          _id: '1',
          name: 'iPhone 15 Pro Max',
          price: 29990000,
          images: [{ url: '/mock-iphone.jpg' }]
        },
        {
          _id: '2',
          name: 'Samsung Galaxy S24',
          price: 22990000,
          images: [{ url: '/mock-samsung.jpg' }]
        }
      ].filter(item => 
        item.name.toLowerCase().includes(query.toLowerCase())
      );
      
      const options = mockSuggestions.map(product => ({
        value: product.name,
        label: (
          <div className="suggestion-item" onClick={() => handleProductSelect(product)}>
            <Image
              src={product.images?.[0]?.url || '/placeholder-image.jpg'}
              alt={product.name}
              width={40}
              height={40}
              preview={false}
            />
            <div className="suggestion-content">
              <Text strong>{product.name}</Text>
              <Text type="secondary">
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND'
                }).format(product.price)}
              </Text>
            </div>
          </div>
        ),
        product
      }));
      
      setSuggestions(options);
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value) => {
    if (value.trim()) {
      if (onSearch) {
        onSearch(value);
      } else {
        navigate(`${LINK.SHOP.SEARCH}?q=${encodeURIComponent(value)}`);
      }
    }
  };

  const handleProductSelect = (product) => {
    navigate(LINK.SHOP.PRODUCT_DETAIL.format(product._id));
    setSearchValue('');
    setSuggestions([]);
  };

  const handleInputChange = (value) => {
    setSearchValue(value);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch(searchValue);
    }
  };

  return (
    <div className="search-bar">
      <AutoComplete
        value={searchValue}
        options={suggestions}
        onSearch={handleInputChange}
        onSelect={(value, option) => {
          if (option.product) {
            handleProductSelect(option.product);
          } else {
            handleSearch(value);
          }
        }}
        className="search-autocomplete"
        dropdownClassName="search-dropdown"
        notFoundContent={loading ? 'Đang tìm kiếm...' : 'Không tìm thấy sản phẩm'}
      >
        <Input
          size={size}
          placeholder={placeholder}
          prefix={<SearchOutlined />}
          onPressEnter={handleKeyPress}
          className="search-input"
        />
      </AutoComplete>
    </div>
  );
};

export default SearchBar;

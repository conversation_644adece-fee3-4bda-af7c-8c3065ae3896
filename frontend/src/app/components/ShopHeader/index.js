import React, { useState, useEffect } from 'react';
import { Layout, Row, Col, Space, Drawer, Button } from 'antd';
import { MenuOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import ShopNavigation from '@app/components/ShopNavigation';
import SearchBar from '@app/components/SearchBar';
import { LINK } from '@link';
import './ShopHeader.scss';

const { Header } = Layout;

const ShopHeader = ({ cartItemCount = 0, wishlistCount = 0 }) => {
  const navigate = useNavigate();
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  const handleLogoClick = () => {
    navigate(LINK.SHOP.HOME);
  };

  const handleSearch = (searchQuery) => {
    navigate(`${LINK.SHOP.SEARCH}?q=${encodeURIComponent(searchQuery)}`);
  };

  const toggleMobileMenu = () => {
    setMobileMenuVisible(!mobileMenuVisible);
  };

  return (
    <Header className="shop-header">
      <div className="header-container">
        <Row align="middle" justify="space-between" className="header-row">
          {/* Logo */}
          <Col xs={8} sm={6} md={4} lg={3}>
            <div className="logo" onClick={handleLogoClick}>
              <img 
                src="/logo.png" 
                alt="Shop Logo" 
                className="logo-image"
                onError={(e) => {
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'block';
                }}
              />
              <span className="logo-text" style={{ display: 'none' }}>
                MyShop
              </span>
            </div>
          </Col>

          {/* Search Bar - Desktop */}
          {!isMobile && (
            <Col xs={0} sm={0} md={12} lg={14}>
              <div className="search-container">
                <SearchBar 
                  onSearch={handleSearch}
                  placeholder="Tìm kiếm sản phẩm..."
                  size="large"
                />
              </div>
            </Col>
          )}

          {/* Navigation - Desktop */}
          {!isMobile && (
            <Col xs={0} sm={0} md={8} lg={7}>
              <ShopNavigation 
                cartItemCount={cartItemCount}
                wishlistCount={wishlistCount}
              />
            </Col>
          )}

          {/* Mobile Menu Button */}
          {isMobile && (
            <Col xs={16} sm={18}>
              <div className="mobile-actions">
                <Space size="middle">
                  <ShopNavigation 
                    cartItemCount={cartItemCount}
                    wishlistCount={wishlistCount}
                  />
                  <Button
                    type="text"
                    icon={<MenuOutlined />}
                    onClick={toggleMobileMenu}
                    className="mobile-menu-button"
                  />
                </Space>
              </div>
            </Col>
          )}
        </Row>

        {/* Search Bar - Mobile */}
        {isMobile && (
          <Row className="mobile-search-row">
            <Col span={24}>
              <SearchBar 
                onSearch={handleSearch}
                placeholder="Tìm kiếm sản phẩm..."
                size="middle"
              />
            </Col>
          </Row>
        )}
      </div>

      {/* Mobile Menu Drawer */}
      <Drawer
        title="Menu"
        placement="right"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        className="mobile-menu-drawer"
      >
        <div className="mobile-menu-content">
          <div className="menu-item" onClick={() => {
            navigate(LINK.SHOP.HOME);
            setMobileMenuVisible(false);
          }}>
            Trang chủ
          </div>
          <div className="menu-item" onClick={() => {
            navigate(LINK.SHOP.PRODUCTS);
            setMobileMenuVisible(false);
          }}>
            Sản phẩm
          </div>
          <div className="menu-item" onClick={() => {
            navigate(LINK.ABOUT);
            setMobileMenuVisible(false);
          }}>
            Giới thiệu
          </div>
          <div className="menu-item" onClick={() => {
            navigate(LINK.CONTACT);
            setMobileMenuVisible(false);
          }}>
            Liên hệ
          </div>
        </div>
      </Drawer>
    </Header>
  );
};

export default ShopHeader;

.shop-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 0;
  
  .header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
    
    .header-row {
      height: 64px;
    }
    
    .mobile-search-row {
      padding: 8px 0 16px 0;
      border-top: 1px solid #f0f0f0;
    }
  }
  
  .logo {
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 40px;
    
    .logo-image {
      height: 100%;
      width: auto;
      object-fit: contain;
    }
    
    .logo-text {
      font-size: 24px;
      font-weight: bold;
      color: #1890ff;
      margin-left: 8px;
    }
  }
  
  .search-container {
    display: flex;
    justify-content: center;
    padding: 0 16px;
  }
  
  .mobile-actions {
    display: flex;
    justify-content: flex-end;
    width: 100%;
    
    .mobile-menu-button {
      font-size: 18px;
    }
  }
}

.mobile-menu-drawer {
  .mobile-menu-content {
    .menu-item {
      padding: 16px 0;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      font-size: 16px;
      transition: color 0.3s ease;
      
      &:hover {
        color: #1890ff;
      }
      
      &:last-child {
        border-bottom: none;
      }
    }
  }
}

@media (max-width: 768px) {
  .shop-header {
    .header-container {
      padding: 0 12px;
      
      .header-row {
        height: 56px;
      }
    }
    
    .logo {
      height: 32px;
      
      .logo-text {
        font-size: 20px;
      }
    }
  }
}

@media (max-width: 480px) {
  .shop-header {
    .header-container {
      padding: 0 8px;
      
      .header-row {
        height: 48px;
      }
    }
    
    .logo {
      height: 28px;
      
      .logo-text {
        font-size: 18px;
      }
    }
  }
}

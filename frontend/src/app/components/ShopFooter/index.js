import React from 'react';
import { Layout, Row, Col, Typography, Space, Divider } from 'antd';
import { 
  PhoneOutlined, 
  MailOutlined, 
  EnvironmentOutlined,
  FacebookOutlined,
  TwitterOutlined,
  InstagramOutlined,
  YoutubeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { LINK } from '@link';
import './ShopFooter.scss';

const { Footer } = Layout;
const { Title, Text, Link } = Typography;

const ShopFooter = () => {
  const navigate = useNavigate();

  const handleLinkClick = (path) => {
    navigate(path);
  };

  return (
    <Footer className="shop-footer">
      <div className="footer-container">
        <Row gutter={[32, 32]}>
          {/* Company Info */}
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <div className="footer-logo">
                <img 
                  src="/logo.png" 
                  alt="Shop Logo" 
                  className="logo-image"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
                <Title level={4} className="logo-text" style={{ display: 'none' }}>
                  MyShop
                </Title>
              </div>
              <Text className="company-description">
                Cửa hàng trực tuyến hàng đầu với hàng ngàn sản phẩm chất lượng cao, 
                giá cả hợp lý và dịch vụ khách hàng tuyệt vời.
              </Text>
              <div className="social-links">
                <Space size="middle">
                  <Link href="#" className="social-link">
                    <FacebookOutlined />
                  </Link>
                  <Link href="#" className="social-link">
                    <TwitterOutlined />
                  </Link>
                  <Link href="#" className="social-link">
                    <InstagramOutlined />
                  </Link>
                  <Link href="#" className="social-link">
                    <YoutubeOutlined />
                  </Link>
                </Space>
              </div>
            </div>
          </Col>

          {/* Quick Links */}
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={5} className="section-title">Liên kết nhanh</Title>
              <div className="footer-links">
                <Link onClick={() => handleLinkClick(LINK.SHOP.HOME)}>
                  Trang chủ
                </Link>
                <Link onClick={() => handleLinkClick(LINK.SHOP.PRODUCTS)}>
                  Sản phẩm
                </Link>
                <Link onClick={() => handleLinkClick(LINK.ABOUT)}>
                  Giới thiệu
                </Link>
                <Link onClick={() => handleLinkClick(LINK.CONTACT)}>
                  Liên hệ
                </Link>
                <Link onClick={() => handleLinkClick(LINK.HELP)}>
                  Trợ giúp
                </Link>
              </div>
            </div>
          </Col>

          {/* Customer Service */}
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={5} className="section-title">Chăm sóc khách hàng</Title>
              <div className="footer-links">
                <Link onClick={() => handleLinkClick(LINK.SHOP.ORDERS)}>
                  Theo dõi đơn hàng
                </Link>
                <Link onClick={() => handleLinkClick(LINK.RETURN_POLICY)}>
                  Chính sách đổi trả
                </Link>
                <Link onClick={() => handleLinkClick(LINK.SHIPPING_POLICY)}>
                  Chính sách vận chuyển
                </Link>
                <Link onClick={() => handleLinkClick(LINK.WARRANTY_POLICY)}>
                  Chính sách bảo hành
                </Link>
                <Link onClick={() => handleLinkClick(LINK.PAYMENT_POLICY)}>
                  Hướng dẫn thanh toán
                </Link>
              </div>
            </div>
          </Col>

          {/* Contact Info */}
          <Col xs={24} sm={12} md={6}>
            <div className="footer-section">
              <Title level={5} className="section-title">Thông tin liên hệ</Title>
              <div className="contact-info">
                <div className="contact-item">
                  <EnvironmentOutlined className="contact-icon" />
                  <Text>123 Đường ABC, Quận XYZ, TP. Hồ Chí Minh</Text>
                </div>
                <div className="contact-item">
                  <PhoneOutlined className="contact-icon" />
                  <Text>Hotline: 1900 1234</Text>
                </div>
                <div className="contact-item">
                  <MailOutlined className="contact-icon" />
                  <Text>Email: <EMAIL></Text>
                </div>
              </div>
            </div>
          </Col>
        </Row>

        <Divider />

        {/* Bottom Section */}
        <Row justify="space-between" align="middle" className="footer-bottom">
          <Col xs={24} md={12}>
            <Text type="secondary" className="copyright">
              © 2024 MyShop. Tất cả quyền được bảo lưu.
            </Text>
          </Col>
          <Col xs={24} md={12}>
            <div className="footer-bottom-links">
              <Space split={<Divider type="vertical" />}>
                <Link onClick={() => handleLinkClick(LINK.TERMS_OF_SERVICE)}>
                  Điều khoản dịch vụ
                </Link>
                <Link onClick={() => handleLinkClick(LINK.PRIVACY_POLICY)}>
                  Chính sách bảo mật
                </Link>
              </Space>
            </div>
          </Col>
        </Row>
      </div>
    </Footer>
  );
};

export default ShopFooter;

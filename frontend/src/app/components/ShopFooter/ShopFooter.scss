.shop-footer {
  background: #001529;
  color: #fff;
  margin-top: auto;
  
  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px 16px 20px;
  }
  
  .footer-section {
    .footer-logo {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      
      .logo-image {
        height: 40px;
        width: auto;
        object-fit: contain;
        filter: brightness(0) invert(1);
      }
      
      .logo-text {
        color: #fff !important;
        margin: 0 0 0 8px;
      }
    }
    
    .company-description {
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
      margin-bottom: 16px;
      display: block;
    }
    
    .social-links {
      .social-link {
        color: rgba(255, 255, 255, 0.8);
        font-size: 20px;
        transition: color 0.3s ease;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
    
    .section-title {
      color: #fff !important;
      margin-bottom: 16px;
      font-size: 16px;
    }
    
    .footer-links {
      display: flex;
      flex-direction: column;
      gap: 8px;
      
      .ant-typography {
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: color 0.3s ease;
        
        &:hover {
          color: #1890ff;
        }
      }
    }
    
    .contact-info {
      .contact-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 12px;
        
        .contact-icon {
          color: #1890ff;
          margin-top: 2px;
          flex-shrink: 0;
        }
        
        .ant-typography {
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.5;
        }
      }
    }
  }
  
  .ant-divider {
    border-color: rgba(255, 255, 255, 0.2);
    margin: 32px 0 20px;
  }
  
  .footer-bottom {
    .copyright {
      color: rgba(255, 255, 255, 0.6);
    }
    
    .footer-bottom-links {
      text-align: right;
      
      .ant-typography {
        color: rgba(255, 255, 255, 0.8);
        cursor: pointer;
        transition: color 0.3s ease;
        
        &:hover {
          color: #1890ff;
        }
      }
      
      .ant-divider {
        border-color: rgba(255, 255, 255, 0.3);
        margin: 0 8px;
      }
    }
  }
}

@media (max-width: 768px) {
  .shop-footer {
    .footer-container {
      padding: 32px 12px 16px;
    }
    
    .footer-section {
      .footer-logo {
        .logo-image {
          height: 32px;
        }
        
        .logo-text {
          font-size: 18px;
        }
      }
      
      .section-title {
        font-size: 14px;
      }
      
      .company-description {
        font-size: 14px;
      }
      
      .footer-links {
        .ant-typography {
          font-size: 14px;
        }
      }
      
      .contact-info {
        .contact-item {
          .ant-typography {
            font-size: 14px;
          }
        }
      }
    }
    
    .footer-bottom {
      text-align: center;
      
      .footer-bottom-links {
        text-align: center;
        margin-top: 12px;
      }
    }
  }
}

@media (max-width: 480px) {
  .shop-footer {
    .footer-container {
      padding: 24px 8px 12px;
    }
    
    .footer-section {
      .company-description,
      .footer-links .ant-typography,
      .contact-info .contact-item .ant-typography {
        font-size: 13px;
      }
    }
    
    .footer-bottom {
      .copyright,
      .footer-bottom-links .ant-typography {
        font-size: 12px;
      }
    }
  }
}

.product-card {
  .product-image-container {
    position: relative;
    overflow: hidden;
    cursor: pointer;
    
    .product-image {
      width: 100%;
      height: 200px;
      object-fit: cover;
      transition: transform 0.3s ease;
    }
    
    &:hover .product-image {
      transform: scale(1.05);
    }
    
    .product-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover .product-overlay {
      opacity: 1;
    }
  }
  
  .product-info {
    .product-name {
      margin-bottom: 8px;
      min-height: 48px;
    }
    
    .product-rating {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      
      .rating-count {
        font-size: 12px;
      }
    }
    
    .product-price {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;
      
      .current-price {
        font-size: 16px;
        color: #ff4d4f;
      }
      
      .original-price {
        font-size: 14px;
      }
    }
    
    .out-of-stock,
    .low-stock {
      font-size: 12px;
      margin-bottom: 4px;
    }
    
    .featured-badge,
    .new-badge {
      margin-top: 4px;
    }
  }
  
  .ant-card-actions {
    li {
      margin: 0;
      
      &:first-child {
        width: 30%;
      }
      
      &:last-child {
        width: 70%;
      }
    }
  }
}

@media (max-width: 768px) {
  .product-card {
    .product-image-container {
      .product-image {
        height: 150px;
      }
    }
    
    .product-info {
      .product-name {
        font-size: 14px;
        min-height: 40px;
      }
      
      .product-price {
        .current-price {
          font-size: 14px;
        }
      }
    }
  }
}

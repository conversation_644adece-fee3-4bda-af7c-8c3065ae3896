import React from 'react';
import { Card, Button, Rate, Badge, Typography, Space, Image } from 'antd';
import {
  ShoppingCartOutlined,
  HeartOutlined,
  EyeOutlined,
  HeartFilled
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { LINK } from '@link';
import './ProductCard.scss';

const { Text, Title } = Typography;

const ProductCard = ({
  product,
  onAddToCart,
  onToggleWishlist,
  isInWishlist = false,
  loading = false
}) => {
  const navigate = useNavigate();

  const handleViewProduct = () => {
    navigate(LINK.SHOP.PRODUCT_DETAIL.format(product._id));
  };

  const handleAddToCart = (e) => {
    e.stopPropagation();
    if (onAddToCart) {
      onAddToCart(product);
    }
  };

  const handleToggleWishlist = (e) => {
    e.stopPropagation();
    if (onToggleWishlist) {
      onToggleWishlist(product);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const getDiscountPercent = () => {
    if (product.originalPrice && product.price < product.originalPrice) {
      return Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100);
    }
    return 0;
  };

  const discountPercent = getDiscountPercent();

  return (
    <Card
      className="product-card"
      hoverable
      loading={loading}
      cover={
        <div className="product-image-container" onClick={handleViewProduct}>
          <Image
            alt={product.name}
            src={product.images?.[0]?.url || '/placeholder-image.jpg'}
            preview={false}
            className="product-image"
          />
          {discountPercent > 0 && (
            <Badge.Ribbon text={`-${discountPercent}%`} color="red" />
          )}
          <div className="product-overlay">
            <Space>
              <Button
                type="primary"
                icon={<EyeOutlined />}
                size="small"
                onClick={handleViewProduct}
              >
                Xem chi tiết
              </Button>
            </Space>
          </div>
        </div>
      }
      actions={[
        <Button
          key="wishlist"
          type="text"
          icon={isInWishlist ? <HeartFilled style={{ color: '#ff4d4f' }} /> : <HeartOutlined />}
          onClick={handleToggleWishlist}
        />,
        <Button
          key="cart"
          type="primary"
          icon={<ShoppingCartOutlined />}
          onClick={handleAddToCart}
          disabled={product.totalStock === 0}
        >
          Thêm vào giỏ
        </Button>
      ]}
    >
      <div className="product-info">
        <Title level={5} ellipsis={{ rows: 2 }} className="product-name">
          {product.name}
        </Title>

        <div className="product-rating">
          <Rate disabled defaultValue={product.averageRating || 0} size="small" />
          <Text type="secondary" className="rating-count">
            ({product.totalReviews || 0})
          </Text>
        </div>

        <div className="product-price">
          <Text strong className="current-price">
            {formatPrice(product.price)}
          </Text>
          {product.originalPrice && product.originalPrice > product.price && (
            <Text delete type="secondary" className="original-price">
              {formatPrice(product.originalPrice)}
            </Text>
          )}
        </div>

        {product.totalStock === 0 && (
          <Text type="danger" className="out-of-stock">
            Hết hàng
          </Text>
        )}

        {product.totalStock > 0 && product.totalStock <= 5 && (
          <Text type="warning" className="low-stock">
            Chỉ còn {product.totalStock} sản phẩm
          </Text>
        )}

        {product.isFeatured && (
          <Badge status="success" text="Nổi bật" className="featured-badge" />
        )}

        {product.isNewProduct && (
          <Badge status="processing" text="Mới" className="new-badge" />
        )}
      </div>
    </Card>
  );
};

export default ProductCard;

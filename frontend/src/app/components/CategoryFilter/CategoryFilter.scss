.category-filter {
  .ant-card-head {
    border-bottom: 1px solid #f0f0f0;
    
    .ant-card-head-title {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .ant-collapse {
    .ant-collapse-item {
      border: none;
      
      .ant-collapse-header {
        padding: 12px 0;
        font-weight: 500;
        
        .ant-collapse-arrow {
          right: 0;
        }
      }
      
      .ant-collapse-content {
        border: none;
        
        .ant-collapse-content-box {
          padding: 0 0 16px 0;
        }
      }
    }
  }
  
  .category-tree {
    .ant-tree-node-content-wrapper {
      padding: 2px 4px;
      border-radius: 4px;
      
      &:hover {
        background-color: #f5f5f5;
      }
    }
    
    .ant-tree-checkbox {
      margin-right: 8px;
    }
    
    .ant-tree-title {
      font-size: 14px;
    }
  }
  
  .price-filter {
    .ant-slider {
      margin: 16px 0;
    }
    
    .price-display {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
      
      .ant-typography {
        font-size: 12px;
        color: #666;
      }
    }
  }
  
  .rating-filter {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .ant-rate {
      font-size: 16px;
    }
    
    .rating-text {
      font-size: 12px;
    }
  }
  
  .options-filter {
    width: 100%;
    
    .ant-checkbox-wrapper {
      margin: 0;
      padding: 4px 0;
      
      .ant-checkbox {
        margin-right: 8px;
      }
      
      span:last-child {
        font-size: 14px;
      }
    }
  }
}

@media (max-width: 768px) {
  .category-filter {
    .ant-card-head {
      .ant-card-head-title {
        font-size: 14px;
      }
    }
    
    .category-tree {
      .ant-tree-title {
        font-size: 13px;
      }
    }
    
    .price-filter {
      .price-display {
        .ant-typography {
          font-size: 11px;
        }
      }
    }
    
    .rating-filter {
      .ant-rate {
        font-size: 14px;
      }
      
      .rating-text {
        font-size: 11px;
      }
    }
    
    .options-filter {
      .ant-checkbox-wrapper {
        span:last-child {
          font-size: 13px;
        }
      }
    }
  }
}

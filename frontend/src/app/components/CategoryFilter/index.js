import React, { useState, useEffect } from 'react';
import {
  Card,
  Tree,
  Slider,
  Button,
  Space,
  Typography,
  Rate,
  Checkbox,
  Divider,
  Collapse
} from 'antd';
import {
  FilterOutlined,
  ClearOutlined,
  DownOutlined
} from '@ant-design/icons';
import './CategoryFilter.scss';

const { Title, Text } = Typography;
const { Panel } = Collapse;

const CategoryFilter = ({
  categories = [],
  onFilterChange,
  initialFilters = {},
  loading = false
}) => {
  const [filters, setFilters] = useState({
    categoryIds: [],
    priceRange: [0, 50000000],
    rating: 0,
    inStock: false,
    isFeatured: false,
    isNew: false,
    ...initialFilters
  });

  useEffect(() => {
    if (onFilterChange) {
      onFilterChange(filters);
    }
  }, [filters, onFilterChange]);

  const handleCategoryChange = (checkedKeys) => {
    setFilters(prev => ({
      ...prev,
      categoryIds: checkedKeys
    }));
  };

  const handlePriceChange = (value) => {
    setFilters(prev => ({
      ...prev,
      priceRange: value
    }));
  };

  const handleRatingChange = (value) => {
    setFilters(prev => ({
      ...prev,
      rating: value
    }));
  };

  const handleCheckboxChange = (field, checked) => {
    setFilters(prev => ({
      ...prev,
      [field]: checked
    }));
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      categoryIds: [],
      priceRange: [0, 50000000],
      rating: 0,
      inStock: false,
      isFeatured: false,
      isNewProduct: false
    };
    setFilters(clearedFilters);
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0
    }).format(price);
  };

  const buildCategoryTree = (categories, parentId = null) => {
    return categories
      .filter(cat => cat.parentId === parentId)
      .map(category => ({
        title: category.name,
        key: category._id,
        children: buildCategoryTree(categories, category._id)
      }));
  };

  const categoryTreeData = buildCategoryTree(categories);

  return (
    <Card
      className="category-filter"
      title={
        <Space>
          <FilterOutlined />
          <span>Bộ lọc</span>
        </Space>
      }
      extra={
        <Button
          type="link"
          size="small"
          icon={<ClearOutlined />}
          onClick={handleClearFilters}
        >
          Xóa bộ lọc
        </Button>
      }
      loading={loading}
    >
      <Collapse
        defaultActiveKey={['categories', 'price', 'rating', 'options']}
        ghost
        expandIcon={({ isActive }) => <DownOutlined rotate={isActive ? 180 : 0} />}
      >
        {/* Categories */}
        <Panel header="Danh mục" key="categories">
          <Tree
            checkable
            checkedKeys={filters.categoryIds}
            onCheck={handleCategoryChange}
            treeData={categoryTreeData}
            className="category-tree"
          />
        </Panel>

        {/* Price Range */}
        <Panel header="Khoảng giá" key="price">
          <div className="price-filter">
            <Slider
              range
              min={0}
              max={50000000}
              step={100000}
              value={filters.priceRange}
              onChange={handlePriceChange}
              tooltip={{
                formatter: formatPrice
              }}
            />
            <div className="price-display">
              <Text>{formatPrice(filters.priceRange[0])}</Text>
              <Text>{formatPrice(filters.priceRange[1])}</Text>
            </div>
          </div>
        </Panel>

        {/* Rating */}
        <Panel header="Đánh giá" key="rating">
          <div className="rating-filter">
            <Rate
              value={filters.rating}
              onChange={handleRatingChange}
              allowClear
            />
            <Text type="secondary" className="rating-text">
              {filters.rating > 0 ? `${filters.rating} sao trở lên` : 'Tất cả'}
            </Text>
          </div>
        </Panel>

        {/* Additional Options */}
        <Panel header="Tùy chọn khác" key="options">
          <Space direction="vertical" className="options-filter">
            <Checkbox
              checked={filters.inStock}
              onChange={(e) => handleCheckboxChange('inStock', e.target.checked)}
            >
              Còn hàng
            </Checkbox>
            <Checkbox
              checked={filters.isFeatured}
              onChange={(e) => handleCheckboxChange('isFeatured', e.target.checked)}
            >
              Sản phẩm nổi bật
            </Checkbox>
            <Checkbox
              checked={filters.isNewProduct}
              onChange={(e) => handleCheckboxChange('isNewProduct', e.target.checked)}
            >
              Sản phẩm mới
            </Checkbox>
          </Space>
        </Panel>
      </Collapse>
    </Card>
  );
};

export default CategoryFilter;

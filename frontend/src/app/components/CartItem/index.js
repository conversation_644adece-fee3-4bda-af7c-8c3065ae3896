import React, { useState } from 'react';
import { Card, InputNumber, Button, Typography, Space, Image, Popconfirm } from 'antd';
import { DeleteOutlined, PlusOutlined, MinusOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { LINK } from '@link';
import './CartItem.scss';

const { Text, Title } = Typography;

const CartItem = ({ 
  item, 
  onUpdateQuantity, 
  onRemoveItem, 
  loading = false 
}) => {
  const navigate = useNavigate();
  const [quantity, setQuantity] = useState(item.quantity);

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity >= 1 && newQuantity <= item.variantId.totalStock) {
      setQuantity(newQuantity);
      if (onUpdateQuantity) {
        onUpdateQuantity(item._id, newQuantity);
      }
    }
  };

  const handleRemoveItem = () => {
    if (onRemoveItem) {
      onRemoveItem(item._id);
    }
  };

  const handleViewProduct = () => {
    navigate(LINK.SHOP.PRODUCT_DETAIL.format(item.variantId.productId._id));
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const getVariantAttributes = () => {
    if (!item.variantId.attributes || item.variantId.attributes.length === 0) {
      return null;
    }
    
    return item.variantId.attributes.map(attr => `${attr.name}: ${attr.value}`).join(', ');
  };

  return (
    <Card className="cart-item" loading={loading}>
      <div className="cart-item-content">
        <div className="item-image" onClick={handleViewProduct}>
          <Image
            src={item.variantId.productId.images?.[0]?.url || '/placeholder-image.jpg'}
            alt={item.variantId.productId.name}
            preview={false}
            width={80}
            height={80}
          />
        </div>

        <div className="item-details">
          <Title 
            level={5} 
            className="item-name" 
            onClick={handleViewProduct}
          >
            {item.variantId.productId.name}
          </Title>
          
          {getVariantAttributes() && (
            <Text type="secondary" className="item-variant">
              {getVariantAttributes()}
            </Text>
          )}
          
          <div className="item-price">
            <Text strong>{formatPrice(item.variantId.price)}</Text>
          </div>

          {item.variantId.totalStock <= 5 && (
            <Text type="warning" className="stock-warning">
              Chỉ còn {item.variantId.totalStock} sản phẩm
            </Text>
          )}
        </div>

        <div className="item-actions">
          <div className="quantity-controls">
            <Button
              size="small"
              icon={<MinusOutlined />}
              onClick={() => handleQuantityChange(quantity - 1)}
              disabled={quantity <= 1 || loading}
            />
            <InputNumber
              size="small"
              min={1}
              max={item.variantId.totalStock}
              value={quantity}
              onChange={handleQuantityChange}
              disabled={loading}
              className="quantity-input"
            />
            <Button
              size="small"
              icon={<PlusOutlined />}
              onClick={() => handleQuantityChange(quantity + 1)}
              disabled={quantity >= item.variantId.totalStock || loading}
            />
          </div>

          <div className="item-total">
            <Text strong className="total-price">
              {formatPrice(item.itemTotal)}
            </Text>
          </div>

          <Popconfirm
            title="Xóa sản phẩm"
            description="Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?"
            onConfirm={handleRemoveItem}
            okText="Xóa"
            cancelText="Hủy"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              disabled={loading}
              className="remove-button"
            />
          </Popconfirm>
        </div>
      </div>
    </Card>
  );
};

export default CartItem;

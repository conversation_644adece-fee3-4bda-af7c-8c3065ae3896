.cart-item {
  margin-bottom: 16px;
  
  .cart-item-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    
    .item-image {
      cursor: pointer;
      flex-shrink: 0;
      
      .ant-image {
        border-radius: 8px;
        overflow: hidden;
      }
    }
    
    .item-details {
      flex: 1;
      min-width: 0;
      
      .item-name {
        margin-bottom: 4px;
        cursor: pointer;
        transition: color 0.3s ease;
        
        &:hover {
          color: #1890ff;
        }
      }
      
      .item-variant {
        display: block;
        margin-bottom: 8px;
        font-size: 12px;
      }
      
      .item-price {
        margin-bottom: 4px;
        
        span {
          font-size: 16px;
          color: #ff4d4f;
        }
      }
      
      .stock-warning {
        font-size: 12px;
      }
    }
    
    .item-actions {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 12px;
      flex-shrink: 0;
      
      .quantity-controls {
        display: flex;
        align-items: center;
        gap: 4px;
        
        .quantity-input {
          width: 60px;
          text-align: center;
          
          .ant-input-number-input {
            text-align: center;
          }
        }
      }
      
      .item-total {
        .total-price {
          font-size: 18px;
          color: #ff4d4f;
        }
      }
      
      .remove-button {
        padding: 4px;
      }
    }
  }
}

@media (max-width: 768px) {
  .cart-item {
    .cart-item-content {
      flex-direction: column;
      gap: 12px;
      
      .item-image {
        align-self: center;
      }
      
      .item-details {
        text-align: center;
        
        .item-name {
          font-size: 16px;
        }
      }
      
      .item-actions {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        
        .quantity-controls {
          order: 1;
        }
        
        .item-total {
          order: 2;
        }
        
        .remove-button {
          order: 3;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .cart-item {
    .cart-item-content {
      .item-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
        
        .quantity-controls {
          justify-content: center;
        }
        
        .item-total {
          text-align: center;
        }
        
        .remove-button {
          align-self: center;
        }
      }
    }
  }
}

import React, { useState } from 'react';
import './Welcome.scss';
import { Layout, Typography, Button, Card, Row, Col, Space, Badge, Statistic } from 'antd';
import {
  ShoppingCartOutlined,
  StarFilled,
  TruckOutlined,
  SafetyOutlined,
  PhoneOutlined,
  LaptopOutlined,
  TabletOutlined,
  CameraOutlined,
  HeadphonesOutlined,
  WatchOutlined,
  HomeOutlined,
  TagsOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { LINK } from '@link';

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const Welcome = () => {
  const navigate = useNavigate();

  const handleCategoryClick = (categorySlug) => {
    navigate(LINK.SHOP.CATEGORY.replace(':slug', categorySlug));
  };

  const handleShopNow = () => {
    navigate(LINK.SHOP.PRODUCTS);
  };

  return (
    <Layout className="welcome-page">
      <Content>
        {/* Hero Section */}
        <div className="hero-section">
          <div className="container">
            <div className="hero-content">
              <Title level={1} className="hero-title">
                Chào mừng đến với <span className="highlight">ShopMart</span>
              </Title>
              <Paragraph className="hero-subtitle">
                Khám phá hàng nghìn sản phẩm chất lượng cao với giá tốt nhất.
                Mua sắm dễ dàng, giao hàng nhanh chóng, uy tín hàng đầu.
              </Paragraph>
              <Space size="large" className="hero-actions">
                <Button
                  type="primary"
                  size="large"
                  icon={<ShoppingCartOutlined />}
                  onClick={handleShopNow}
                  className="shop-now-btn"
                >
                  Mua sắm ngay
                </Button>
                <Button
                  size="large"
                  onClick={() => navigate(LINK.ABOUT)}
                  className="learn-more-btn"
                >
                  Tìm hiểu thêm
                </Button>
              </Space>
            </div>
            <div className="hero-stats">
              <Row gutter={[24, 24]}>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="Sản phẩm"
                    value={10000}
                    suffix="+"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="Khách hàng"
                    value={50000}
                    suffix="+"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="Đánh giá"
                    value={4.8}
                    suffix="/5"
                    prefix={<StarFilled style={{ color: '#faad14' }} />}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Col>
                <Col xs={12} sm={6}>
                  <Statistic
                    title="Thành phố"
                    value={63}
                    suffix="/"
                    precision={0}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
              </Row>
            </div>
          </div>
        </div>

        {/* Categories Section */}
        <div className="categories-section">
          <div className="container">
            <Title level={2} className="section-title">Danh mục sản phẩm</Title>
            <Paragraph className="section-subtitle">
              Khám phá các danh mục sản phẩm đa dạng của chúng tôi
            </Paragraph>
            <Row gutter={[24, 24]} className="category-grid">
              {[
                { icon: <PhoneOutlined />, text: 'Điện thoại', slug: 'dien-thoai', color: '#1890ff' },
                { icon: <LaptopOutlined />, text: 'Laptop', slug: 'laptop', color: '#52c41a' },
                { icon: <TabletOutlined />, text: 'Tablet', slug: 'tablet', color: '#722ed1' },
                { icon: <CameraOutlined />, text: 'Camera', slug: 'camera', color: '#fa541c' },
                { icon: <HeadphonesOutlined />, text: 'Tai nghe', slug: 'tai-nghe', color: '#faad14' },
                { icon: <WatchOutlined />, text: 'Đồng hồ', slug: 'dong-ho', color: '#eb2f96' },
                { icon: <HomeOutlined />, text: 'Gia dụng', slug: 'gia-dung', color: '#13c2c2' },
                { icon: <TagsOutlined />, text: 'Thời trang', slug: 'thoi-trang', color: '#f759ab' },
              ].map((category, index) => (
                <Col xs={12} sm={8} md={6} lg={3} key={index}>
                  <Card
                    className="category-card"
                    onClick={() => handleCategoryClick(category.slug)}
                    hoverable
                  >
                    <div className="category-icon" style={{ color: category.color }}>
                      {category.icon}
                    </div>
                    <Text className="category-text">{category.text}</Text>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>
        </div>
        {/* Benefits Section */}
        <div className="benefits-section">
          <div className="container">
            <Title level={2} className="section-title">Ưu điểm vượt trội</Title>
            <Row gutter={[24, 24]} className="benefits-grid">
              {[
                {
                  icon: <TruckOutlined />,
                  title: 'Giao hàng siêu tốc',
                  description: 'Giao hàng nhanh trong 2-4 giờ tại TP.HCM và Hà Nội',
                  color: '#52c41a'
                },
                {
                  icon: <SafetyOutlined />,
                  title: 'Đảm bảo chất lượng',
                  description: '100% sản phẩm chính hãng, kiểm tra kỹ trước khi giao',
                  color: '#1890ff'
                },
                {
                  icon: <PhoneOutlined />,
                  title: 'Hỗ trợ 24/7',
                  description: 'Đội ngũ chăm sóc khách hàng luôn sẵn sàng hỗ trợ bạn',
                  color: '#722ed1'
                },
                {
                  icon: <TagsOutlined />,
                  title: 'Giá tốt nhất',
                  description: 'Cam kết giá tốt nhất thị trường với nhiều ưu đãi hấp dẫn',
                  color: '#52c41a'
                }
              ].map((benefit, index) => (
                <Col xs={24} sm={12} md={6} key={index}>
                  <Card className="benefit-card" bordered={false}>
                    <div className="benefit-icon" style={{ color: benefit.color }}>
                      {benefit.icon}
                    </div>
                    <Title level={4} className="benefit-title">{benefit.title}</Title>
                    <Paragraph className="benefit-description">{benefit.description}</Paragraph>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>
        </div>
        {/* Call to Action Section */}
        <div className="cta-section">
          <div className="container">
            <Card className="cta-card">
              <div className="cta-content">
                <Title level={2} className="cta-title">
                  Sẵn sàng bắt đầu mua sắm?
                </Title>
                <Paragraph className="cta-description">
                  Tham gia cùng hàng nghìn khách hàng đã tin tưởng và mua sắm tại ShopMart.
                  Trải nghiệm mua sắm tuyệt vời ngay hôm nay!
                </Paragraph>
                <Space size="large" className="cta-actions">
                  <Button
                    type="primary"
                    size="large"
                    icon={<ShoppingCartOutlined />}
                    onClick={handleShopNow}
                  >
                    Khám phá sản phẩm
                  </Button>
                  <Button
                    size="large"
                    onClick={() => navigate(LINK.CONTACT)}
                  >
                    Liên hệ tư vấn
                  </Button>
                </Space>
              </div>
            </Card>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default Welcome;


.product-detail-page {
  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    gap: 16px;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .page-breadcrumb {
    margin-bottom: 2rem;
  }

  .product-detail-section {
    margin-bottom: 3rem;
  }

  // Product Images
  .product-images {
    .main-image {
      position: relative;
      margin-bottom: 1rem;
      border-radius: 12px;
      overflow: hidden;
      background: #f5f5f5;

      .ant-image {
        width: 100%;
        height: 400px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .discount-badge {
        position: absolute;
        top: 16px;
        left: 16px;
        font-size: 14px;
        font-weight: bold;
        z-index: 2;
      }
    }

    .thumbnail-images {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      padding: 4px 0;

      .thumbnail {
        flex-shrink: 0;
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        cursor: pointer;
        border: 2px solid transparent;
        transition: border-color 0.3s ease;

        &.active {
          border-color: #1890ff;
        }

        &:hover {
          border-color: #40a9ff;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  // Product Info
  .product-info {
    .product-name {
      margin-bottom: 1rem;
      color: #1890ff;
    }

    .product-rating {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 1rem;

      .ant-rate {
        font-size: 16px;
      }
    }

    .product-price {
      margin-bottom: 1.5rem;

      .current-price {
        font-size: 2rem;
        font-weight: bold;
        color: #ff4d4f;
        margin-right: 1rem;
      }

      .original-price {
        font-size: 1.2rem;
        color: #999;
      }
    }

    .product-description {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 2rem;
      color: #666;
    }

    .product-variants {
      margin-bottom: 2rem;

      h5 {
        margin-bottom: 1rem;
        color: #333;
      }

      .variant-options {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .variant-card {
          min-width: 120px;
          cursor: pointer;
          border: 2px solid #f0f0f0;
          transition: all 0.3s ease;

          &.selected {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }

          &:hover {
            border-color: #40a9ff;
          }

          .variant-info {
            text-align: center;
            padding: 8px;

            .variant-price {
              display: block;
              font-weight: bold;
              color: #ff4d4f;
              margin-top: 4px;
            }

            .low-stock {
              display: block;
              font-size: 12px;
              margin-top: 4px;
            }
          }
        }
      }
    }

    .quantity-section {
      margin-bottom: 2rem;

      h5 {
        margin-bottom: 1rem;
        color: #333;
      }

      .quantity-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .quantity-input {
          width: 80px;
          text-align: center;

          .ant-input-number-input {
            text-align: center;
          }
        }
      }
    }

    .product-actions {
      display: flex;
      gap: 12px;
      margin-bottom: 1.5rem;

      .add-to-cart-btn {
        flex: 1;
        height: 48px;
        font-size: 16px;
        font-weight: 600;
      }

      .wishlist-btn,
      .share-btn {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .stock-status {
      padding: 12px;
      background: #f6ffed;
      border: 1px solid #b7eb8f;
      border-radius: 8px;
    }
  }

  // Product Details Tabs
  .product-details-tabs {
    margin-bottom: 3rem;

    .ant-tabs {
      .ant-tabs-tab {
        font-size: 16px;
        font-weight: 500;
      }
    }

    .tab-content {
      padding: 2rem 0;
    }

    .product-long-description {
      line-height: 1.8;

      h3 {
        color: #1890ff;
        margin-bottom: 1rem;
      }

      ul {
        padding-left: 1.5rem;

        li {
          margin-bottom: 0.5rem;
          line-height: 1.6;
        }
      }
    }

    .spec-item {
      display: flex;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      .spec-label {
        min-width: 150px;
        color: #666;
      }

      .spec-value {
        flex: 1;
        color: #333;
      }
    }

    .rating-summary {
      margin-bottom: 2rem;

      .overall-rating {
        text-align: center;

        .rating-score {
          margin-bottom: 1rem;

          .score {
            font-size: 3rem;
            font-weight: bold;
            color: #faad14;
            display: block;
            margin-bottom: 0.5rem;
          }

          .ant-rate {
            font-size: 20px;
          }
        }
      }

      .rating-breakdown {
        .rating-bar {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 8px;

          .ant-typography:first-child {
            min-width: 50px;
          }

          .ant-progress {
            flex: 1;
          }

          .ant-typography:last-child {
            min-width: 40px;
            text-align: right;
          }
        }
      }
    }

    .reviews-list {
      .review-card {
        margin-bottom: 1rem;

        .review-header {
          display: flex;
          gap: 12px;
          margin-bottom: 1rem;

          .review-user-info {
            flex: 1;

            .review-meta {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-top: 4px;

              .ant-rate {
                font-size: 14px;
              }
            }
          }
        }

        .review-comment {
          margin-bottom: 1rem;
          line-height: 1.6;
        }

        .review-actions {
          text-align: right;
        }
      }
    }
  }

  // Related Products
  .related-products-section {
    h3 {
      margin-bottom: 1.5rem;
      color: #1890ff;
    }
  }

  // Responsive
  @media (max-width: 768px) {
    .container {
      padding: 1rem 0.5rem;
    }

    .product-images {
      .main-image {
        .ant-image {
          height: 300px;
        }
      }

      .thumbnail-images {
        .thumbnail {
          width: 60px;
          height: 60px;
        }
      }
    }

    .product-info {
      .product-price {
        .current-price {
          font-size: 1.5rem;
        }

        .original-price {
          font-size: 1rem;
        }
      }

      .product-variants {
        .variant-options {
          .variant-card {
            min-width: 100px;
          }
        }
      }

      .product-actions {
        flex-direction: column;

        .add-to-cart-btn {
          order: 1;
        }

        .wishlist-btn,
        .share-btn {
          width: 100%;
          height: 40px;
        }
      }
    }

    .product-details-tabs {
      .rating-summary {
        .overall-rating {
          margin-bottom: 2rem;

          .rating-score {
            .score {
              font-size: 2rem;
            }
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .product-images {
      .main-image {
        .ant-image {
          height: 250px;
        }
      }
    }

    .product-info {
      .product-price {
        .current-price {
          font-size: 1.3rem;
        }
      }

      .quantity-section {
        .quantity-controls {
          justify-content: center;
        }
      }
    }
  }
}

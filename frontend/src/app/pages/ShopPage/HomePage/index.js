import React, { useState, useEffect } from 'react';
import './HomePage.scss';
import { Layout, Typography, Button, Card, Row, Col, Carousel, Space, Tag, message, Spin } from 'antd';
import { ShoppingCartOutlined, HeartOutlined, SearchOutlined, StarFilled, RightOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@app/hooks/useAuth';
import ProductCard from '@app/components/ProductCard';
import SearchBar from '@app/components/SearchBar';
import { LINK } from '@link';
import { API } from '@api';

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const HomePage = () => {
  const navigate = useNavigate();
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [newProducts, setNewProducts] = useState([]);
  const [bestsellerProducts, setBestsellerProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [wishlist, setWishlist] = useState([]);
  const { requireAuth } = useAuth();

  useEffect(() => {
    loadHomePageData();
  }, []);

  const loadHomePageData = async () => {
    setLoading(true);
    try {
      // Load data concurrently
      const [featuredRes, newRes, bestsellerRes, categoriesRes] = await Promise.allSettled([
        // Mock API calls - replace with actual API calls
        Promise.resolve([
          {
            _id: '1',
            name: 'iPhone 15 Pro Max',
            price: 29990000,
            originalPrice: 32990000,
            images: [{ url: '/images/iphone-15-pro.jpg' }],
            averageRating: 4.8,
            totalReviews: 156,
            isFeatured: true,
            totalStock: 50
          },
          {
            _id: '2',
            name: 'Samsung Galaxy S24 Ultra',
            price: 27490000,
            originalPrice: 29990000,
            images: [{ url: '/images/samsung-s24.jpg' }],
            averageRating: 4.7,
            totalReviews: 89,
            isFeatured: true,
            totalStock: 30
          },
          {
            _id: '3',
            name: 'MacBook Air M3',
            price: 31990000,
            originalPrice: 34990000,
            images: [{ url: '/images/macbook-air-m3.jpg' }],
            averageRating: 4.9,
            totalReviews: 234,
            isFeatured: true,
            totalStock: 20
          }
        ]),
        Promise.resolve([
          {
            _id: '4',
            name: 'iPad Pro 13-inch M4',
            price: 32990000,
            images: [{ url: '/images/ipad-pro-m4.jpg' }],
            averageRating: 4.8,
            totalReviews: 67,
            isNew: true,
            totalStock: 25
          },
          {
            _id: '5',
            name: 'AirPods Pro 3',
            price: 6290000,
            images: [{ url: '/images/airpods-pro-3.jpg' }],
            averageRating: 4.6,
            totalReviews: 123,
            isNew: true,
            totalStock: 100
          }
        ]),
        Promise.resolve([
          {
            _id: '6',
            name: 'Sony WH-1000XM5',
            price: 8990000,
            images: [{ url: '/images/sony-headphones.jpg' }],
            averageRating: 4.7,
            totalReviews: 89,
            totalSold: 500,
            totalStock: 40
          }
        ]),
        Promise.resolve([
          { _id: '1', name: 'Điện thoại', slug: 'dien-thoai', icon: '📱', productCount: 150 },
          { _id: '2', name: 'Laptop', slug: 'laptop', icon: '💻', productCount: 80 },
          { _id: '3', name: 'Tablet', slug: 'tablet', icon: '📱', productCount: 45 },
          { _id: '4', name: 'Phụ kiện', slug: 'phu-kien', icon: '🎧', productCount: 200 },
          { _id: '5', name: 'Đồng hồ', slug: 'dong-ho', icon: '⌚', productCount: 60 },
          { _id: '6', name: 'TV', slug: 'tv', icon: '📺', productCount: 35 }
        ])
      ]);

      if (featuredRes.status === 'fulfilled') {
        setFeaturedProducts(featuredRes.value);
      }
      if (newRes.status === 'fulfilled') {
        setNewProducts(newRes.value);
      }
      if (bestsellerRes.status === 'fulfilled') {
        setBestsellerProducts(bestsellerRes.value);
      }
      if (categoriesRes.status === 'fulfilled') {
        setCategories(categoriesRes.value);
      }
    } catch (error) {
      console.error('Error loading homepage data:', error);
      message.error('Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleSearch = (value) => {
    navigate(`${LINK.SHOP.SEARCH}?q=${encodeURIComponent(value)}`);
  };

  const handleAddToCart = (product) => {
    requireAuth(() => {
      console.log('Thêm vào giỏ hàng:', product);
      // TODO: Implement add to cart logic with API
      message.success('Đã thêm sản phẩm vào giỏ hàng!');
    }, {
      title: 'Đăng nhập để mua hàng',
      content: 'Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.'
    });
  };

  const handleToggleWishlist = (product) => {
    requireAuth(() => {
      const isInWishlist = wishlist.some(item => item._id === product._id);
      if (isInWishlist) {
        setWishlist(prev => prev.filter(item => item._id !== product._id));
        message.success('Đã xóa khỏi danh sách yêu thích!');
      } else {
        setWishlist(prev => [...prev, product]);
        message.success('Đã thêm vào danh sách yêu thích!');
      }
    }, {
      title: 'Đăng nhập để lưu yêu thích',
      content: 'Bạn cần đăng nhập để thêm sản phẩm vào danh sách yêu thích.'
    });
  };

  const handleCategoryClick = (category) => {
    navigate(LINK.SHOP.CATEGORY.format(category.slug));
  };

  const handleViewAllProducts = (type) => {
    let url = LINK.SHOP.PRODUCTS;
    if (type === 'featured') {
      url += '?featured=true';
    } else if (type === 'new') {
      url += '?new=true';
    } else if (type === 'bestseller') {
      url += '?bestseller=true';
    }
    navigate(url);
  };

  const isInWishlist = (product) => {
    return wishlist.some(item => item._id === product._id);
  };

  const bannerImages = [
    '/images/banner-1.jpg',
    '/images/banner-2.jpg',
    '/images/banner-3.jpg'
  ];

  if (loading) {
    return (
      <Layout className="shop-homepage">
        <Content>
          <div className="loading-container">
            <Spin size="large" />
            <Text>Đang tải dữ liệu...</Text>
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout className="shop-homepage">
      <Content>
        {/* Hero Banner */}
        <section className="hero-section">
          <Carousel autoplay className="hero-banner">
            {bannerImages.map((image, index) => (
              <div key={index} className="banner-slide">
                <img src={image} alt={`Banner ${index + 1}`} />
                <div className="banner-content">
                  <Title level={1}>Chào mừng đến TechStore</Title>
                  <Paragraph>Khám phá những sản phẩm công nghệ mới nhất với giá tốt nhất</Paragraph>
                  <Button
                    type="primary"
                    size="large"
                    onClick={() => navigate(LINK.SHOP.PRODUCTS)}
                  >
                    Mua sắm ngay
                  </Button>
                </div>
              </div>
            ))}
          </Carousel>
        </section>

        <div className="container">
          {/* Search Section */}
          <section className="search-section">
            <div className="search-container">
              <SearchBar
                onSearch={handleSearch}
                placeholder="Tìm kiếm sản phẩm..."
                size="large"
              />
            </div>
          </section>

          {/* Categories Section */}
          <section className="categories-section">
            <Title level={2}>Danh mục sản phẩm</Title>
            <Row gutter={[16, 16]} className="categories-grid">
              {categories.map((category) => (
                <Col xs={12} sm={8} md={6} lg={4} key={category._id}>
                  <Card
                    className="category-card"
                    hoverable
                    onClick={() => handleCategoryClick(category)}
                  >
                    <div className="category-content">
                      <div className="category-icon">{category.icon}</div>
                      <Title level={4}>{category.name}</Title>
                      <Text type="secondary">{category.productCount} sản phẩm</Text>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </section>

          {/* Featured Products */}
          <section className="featured-section">
            <div className="section-header">
              <Title level={2}>Sản phẩm nổi bật</Title>
              <Button
                type="link"
                icon={<RightOutlined />}
                onClick={() => handleViewAllProducts('featured')}
              >
                Xem tất cả
              </Button>
            </div>
            <Row gutter={[24, 24]} className="products-grid">
              {featuredProducts.map((product) => (
                <Col xs={24} sm={12} md={8} lg={6} key={product._id}>
                  <ProductCard
                    product={product}
                    onAddToCart={handleAddToCart}
                    onToggleWishlist={handleToggleWishlist}
                    isInWishlist={isInWishlist(product)}
                  />
                </Col>
              ))}
            </Row>
          </section>

          {/* Bestseller Products */}
          {bestsellerProducts.length > 0 && (
            <section className="bestseller-section">
              <div className="section-header">
                <Title level={2}>Sản phẩm bán chạy</Title>
                <Button
                  type="link"
                  icon={<RightOutlined />}
                  onClick={() => handleViewAllProducts('bestseller')}
                >
                  Xem tất cả
                </Button>
              </div>
              <Row gutter={[24, 24]} className="products-grid">
                {bestsellerProducts.map((product) => (
                  <Col xs={24} sm={12} md={8} lg={6} key={product._id}>
                    <ProductCard
                      product={product}
                      onAddToCart={handleAddToCart}
                      onToggleWishlist={handleToggleWishlist}
                      isInWishlist={isInWishlist(product)}
                    />
                  </Col>
                ))}
              </Row>
            </section>
          )}

          {/* New Products */}
          <section className="new-products-section">
            <div className="section-header">
              <Title level={2}>Sản phẩm mới</Title>
              <Button
                type="link"
                icon={<RightOutlined />}
                onClick={() => handleViewAllProducts('new')}
              >
                Xem tất cả
              </Button>
            </div>
            <Row gutter={[24, 24]} className="products-grid">
              {newProducts.map((product) => (
                <Col xs={24} sm={12} md={8} lg={6} key={product._id}>
                  <ProductCard
                    product={product}
                    onAddToCart={handleAddToCart}
                    onToggleWishlist={handleToggleWishlist}
                    isInWishlist={isInWishlist(product)}
                  />
                </Col>
              ))}
            </Row>
          </section>

          {/* Promotion Section */}
          <section className="promotion-section">
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Card className="promotion-card" cover={<img src="/images/promo-1.jpg" alt="Khuyến mãi 1" />}>
                  <Title level={3}>Giảm giá lên đến 50%</Title>
                  <Paragraph>Cho tất cả sản phẩm điện thoại</Paragraph>
                  <Button
                    type="primary"
                    onClick={() => navigate(`${LINK.SHOP.PRODUCTS}?category=dien-thoai`)}
                  >
                    Mua ngay
                  </Button>
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card className="promotion-card" cover={<img src="/images/promo-2.jpg" alt="Khuyến mãi 2" />}>
                  <Title level={3}>Miễn phí giao hàng</Title>
                  <Paragraph>Cho đơn hàng từ 2 triệu đồng</Paragraph>
                  <Button
                    type="primary"
                    onClick={() => navigate(LINK.SHIPPING_POLICY)}
                  >
                    Tìm hiểu thêm
                  </Button>
                </Card>
              </Col>
            </Row>
          </section>
        </div>
      </Content>
    </Layout>
  );
};

export default HomePage;


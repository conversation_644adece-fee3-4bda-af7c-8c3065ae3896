import React, { useState, useEffect } from 'react';
import './ProductPage.scss';
import { Layout, Typography, Card, Row, Col, Button, Select, Pagination, Space, Spin, message, Breadcrumb } from 'antd';
import { FilterOutlined, HomeOutlined } from '@ant-design/icons';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useAuth } from '@app/hooks/useAuth';
import ProductCard from '@app/components/ProductCard';
import SearchBar from '@app/components/SearchBar';
import CategoryFilter from '@app/components/CategoryFilter';
import { LINK } from '@link';
import { API } from '@api';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

const ProductPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();

  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [wishlist, setWishlist] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12);
  const [totalProducts, setTotalProducts] = useState(0);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [filters, setFilters] = useState({
    categoryIds: [],
    priceRange: [0, 50000000],
    rating: 0,
    inStock: false,
    isFeatured: false,
    isNew: false
  });
  const { requireAuth } = useAuth();

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    // Parse URL parameters
    const urlParams = {
      q: searchParams.get('q') || '',
      category: searchParams.get('category') || '',
      featured: searchParams.get('featured') === 'true',
      new: searchParams.get('new') === 'true',
      bestseller: searchParams.get('bestseller') === 'true',
      page: parseInt(searchParams.get('page')) || 1,
      sortBy: searchParams.get('sortBy') || 'createdAt',
      sortOrder: searchParams.get('sortOrder') || 'desc'
    };

    setCurrentPage(urlParams.page);
    setSortBy(urlParams.sortBy);
    setSortOrder(urlParams.sortOrder);

    // Update filters based on URL params
    const newFilters = { ...filters };
    if (urlParams.category) {
      newFilters.categoryIds = [urlParams.category];
    }
    if (urlParams.featured) {
      newFilters.isFeatured = true;
    }
    if (urlParams.new) {
      newFilters.isNewProduct = true;
    }
    setFilters(newFilters);

    loadProducts(urlParams);
  }, [searchParams]);

  const loadInitialData = async () => {
    try {
      // Load categories
      const categoriesData = [
        { _id: '1', name: 'Điện thoại', slug: 'dien-thoai' },
        { _id: '2', name: 'Laptop', slug: 'laptop' },
        { _id: '3', name: 'Tablet', slug: 'tablet' },
        { _id: '4', name: 'Phụ kiện', slug: 'phu-kien' },
        { _id: '5', name: 'Đồng hồ', slug: 'dong-ho' },
        { _id: '6', name: 'TV', slug: 'tv' }
      ];
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading initial data:', error);
      message.error('Có lỗi xảy ra khi tải dữ liệu');
    }
  };

  const loadProducts = async (params = {}) => {
    setLoading(true);
    try {
      // Mock API call - replace with actual API
      const mockProducts = [
        {
          _id: '1',
          name: 'iPhone 15 Pro Max',
          price: 29990000,
          originalPrice: 32990000,
          images: [{ url: '/images/iphone-15-pro.jpg' }],
          averageRating: 4.8,
          totalReviews: 156,
          categoryIds: ['1'],
          isFeatured: true,
          totalStock: 50
        },
        {
          _id: '2',
          name: 'Samsung Galaxy S24 Ultra',
          price: 27490000,
          originalPrice: 29990000,
          images: [{ url: '/images/samsung-s24.jpg' }],
          averageRating: 4.7,
          totalReviews: 89,
          categoryIds: ['1'],
          isFeatured: true,
          totalStock: 30
        },
        {
          _id: '3',
          name: 'MacBook Air M3',
          price: 31990000,
          originalPrice: 34990000,
          images: [{ url: '/images/macbook-air-m3.jpg' }],
          averageRating: 4.9,
          totalReviews: 234,
          categoryIds: ['2'],
          isFeatured: true,
          totalStock: 20
        },
        {
          _id: '4',
          name: 'iPad Pro 13-inch M4',
          price: 32990000,
          images: [{ url: '/images/ipad-pro-m4.jpg' }],
          averageRating: 4.8,
          totalReviews: 67,
          categoryIds: ['3'],
          isNew: true,
          totalStock: 25
        },
        {
          _id: '5',
          name: 'AirPods Pro 3',
          price: 6290000,
          images: [{ url: '/images/airpods-pro-3.jpg' }],
          averageRating: 4.6,
          totalReviews: 123,
          categoryIds: ['4'],
          isNew: true,
          totalStock: 100
        },
        {
          _id: '6',
          name: 'Dell XPS 13',
          price: 28990000,
          images: [{ url: '/images/dell-xps-13.jpg' }],
          averageRating: 4.5,
          totalReviews: 98,
          categoryIds: ['2'],
          totalStock: 15
        }
      ];

      // Apply filters (mock filtering)
      let filteredProducts = mockProducts;

      if (params.q) {
        filteredProducts = filteredProducts.filter(product =>
          product.name.toLowerCase().includes(params.q.toLowerCase())
        );
      }

      if (params.category) {
        const category = categories.find(cat => cat.slug === params.category);
        if (category) {
          filteredProducts = filteredProducts.filter(product =>
            product.categoryIds.includes(category._id)
          );
        }
      }

      if (params.featured) {
        filteredProducts = filteredProducts.filter(product => product.isFeatured);
      }

      if (params.new) {
        filteredProducts = filteredProducts.filter(product => product.isNewProduct);
      }

      setProducts(filteredProducts);
      setTotalProducts(filteredProducts.length);
    } catch (error) {
      console.error('Error loading products:', error);
      message.error('Có lỗi xảy ra khi tải sản phẩm');
    } finally {
      setLoading(false);
    }
  };

  const updateUrlParams = (newParams) => {
    const currentParams = Object.fromEntries(searchParams);
    const updatedParams = { ...currentParams, ...newParams };

    // Remove empty params
    Object.keys(updatedParams).forEach(key => {
      if (!updatedParams[key] || updatedParams[key] === '' || updatedParams[key] === 'false') {
        delete updatedParams[key];
      }
    });

    setSearchParams(updatedParams);
  };

  const handleSearch = (searchQuery) => {
    updateUrlParams({ q: searchQuery, page: 1 });
  };

  const handleSortChange = (value) => {
    const [newSortBy, newSortOrder] = value.split('-');
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    updateUrlParams({ sortBy: newSortBy, sortOrder: newSortOrder, page: 1 });
  };

  const handleFilterChange = (newFilters) => {
    setFilters(newFilters);
    const urlParams = {
      page: 1,
      categoryIds: newFilters.categoryIds.join(','),
      minPrice: newFilters.priceRange[0],
      maxPrice: newFilters.priceRange[1],
      rating: newFilters.rating,
      inStock: newFilters.inStock,
      isFeatured: newFilters.isFeatured,
      isNew: newFilters.isNew
    };
    updateUrlParams(urlParams);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    updateUrlParams({ page });
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const handleAddToCart = (product) => {
    requireAuth(() => {
      console.log('Thêm vào giỏ hàng:', product);
      // TODO: Implement add to cart logic with API
      message.success('Đã thêm sản phẩm vào giỏ hàng!');
    }, {
      title: 'Đăng nhập để mua hàng',
      content: 'Bạn cần đăng nhập để thêm sản phẩm vào giỏ hàng.'
    });
  };

  const handleToggleWishlist = (product) => {
    requireAuth(() => {
      const isInWishlist = wishlist.some(item => item._id === product._id);
      if (isInWishlist) {
        setWishlist(prev => prev.filter(item => item._id !== product._id));
        message.success('Đã xóa khỏi danh sách yêu thích!');
      } else {
        setWishlist(prev => [...prev, product]);
        message.success('Đã thêm vào danh sách yêu thích!');
      }
    }, {
      title: 'Đăng nhập để lưu yêu thích',
      content: 'Bạn cần đăng nhập để thêm sản phẩm vào danh sách yêu thích.'
    });
  };

  const isInWishlist = (product) => {
    return wishlist.some(item => item._id === product._id);
  };

  const getBreadcrumbItems = () => {
    const items = [
      {
        title: <HomeOutlined />,
        href: LINK.SHOP.HOME
      },
      {
        title: 'Sản phẩm'
      }
    ];

    const categoryParam = searchParams.get('category');
    if (categoryParam) {
      const category = categories.find(cat => cat.slug === categoryParam);
      if (category) {
        items[items.length - 1] = {
          title: category.name
        };
      }
    }

    return items;
  };

  const paginatedProducts = products.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  if (loading) {
    return (
      <Layout className="product-page">
        <Content>
          <div className="loading-container">
            <Spin size="large" />
            <Text>Đang tải sản phẩm...</Text>
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout className="product-page">
      <Content>
        <div className="container">
          {/* Breadcrumb */}
          <Breadcrumb
            items={getBreadcrumbItems()}
            className="page-breadcrumb"
          />

          {/* Header */}
          <div className="page-header">
            <Title level={2}>
              {searchParams.get('category')
                ? categories.find(cat => cat.slug === searchParams.get('category'))?.name || 'Sản phẩm'
                : 'Tất cả sản phẩm'
              }
            </Title>
            <Text type="secondary">Tìm thấy {totalProducts} sản phẩm</Text>
          </div>

          {/* Search and Sort */}
          <div className="search-sort-section">
            <Row gutter={16} align="middle">
              <Col xs={24} md={16}>
                <SearchBar
                  onSearch={handleSearch}
                  placeholder="Tìm kiếm sản phẩm..."
                  size="large"
                />
              </Col>
              <Col xs={24} md={8}>
                <Select
                  value={`${sortBy}-${sortOrder}`}
                  onChange={handleSortChange}
                  size="large"
                  style={{ width: '100%' }}
                >
                  <Option value="createdAt-desc">Mới nhất</Option>
                  <Option value="price-asc">Giá thấp đến cao</Option>
                  <Option value="price-desc">Giá cao đến thấp</Option>
                  <Option value="averageRating-desc">Đánh giá cao nhất</Option>
                  <Option value="totalSold-desc">Bán chạy nhất</Option>
                </Select>
              </Col>
            </Row>
          </div>

          <Row gutter={24}>
            {/* Filters Sidebar */}
            <Col xs={24} lg={6}>
              <CategoryFilter
                categories={categories}
                onFilterChange={handleFilterChange}
                initialFilters={filters}
                loading={loading}
              />
            </Col>

            {/* Products Grid */}
            <Col xs={24} lg={18}>
              {paginatedProducts.length > 0 ? (
                <>
                  <Row gutter={[16, 16]} className="products-grid">
                    {paginatedProducts.map((product) => (
                      <Col xs={24} sm={12} md={8} xl={6} key={product._id}>
                        <ProductCard
                          product={product}
                          onAddToCart={handleAddToCart}
                          onToggleWishlist={handleToggleWishlist}
                          isInWishlist={isInWishlist(product)}
                        />
                      </Col>
                    ))}
                  </Row>

                  {/* Pagination */}
                  {totalProducts > pageSize && (
                    <div className="pagination-container">
                      <Pagination
                        current={currentPage}
                        pageSize={pageSize}
                        total={totalProducts}
                        onChange={handlePageChange}
                        showSizeChanger={false}
                        showQuickJumper
                        showTotal={(total, range) =>
                          `${range[0]}-${range[1]} của ${total} sản phẩm`
                        }
                      />
                    </div>
                  )}
                </>
              ) : (
                <div className="no-products">
                  <Title level={3}>Không tìm thấy sản phẩm</Title>
                  <Text type="secondary">
                    Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm
                  </Text>
                </div>
              )}
            </Col>
          </Row>
        </div>
      </Content>
    </Layout>
  );
};

export default ProductPage;


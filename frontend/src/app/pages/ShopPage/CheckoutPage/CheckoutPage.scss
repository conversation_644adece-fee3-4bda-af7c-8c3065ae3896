.checkout-page {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    gap: 16px;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .page-breadcrumb {
    margin-bottom: 1rem;
  }

  .empty-cart {
    text-align: center;
    padding: 3rem 1rem;
    border-radius: 8px;
  }

  .checkout-steps {
    margin: 2rem 0 3rem 0;

    .ant-steps-item-title {
      font-weight: 600;
    }
  }

  .checkout-form {
    .ant-card {
      border-radius: 8px;
      margin-bottom: 1.5rem;

      .ant-card-head {
        background: #f8f9fa;
        border-radius: 8px 8px 0 0;

        .ant-card-head-title {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-weight: 600;
        }
      }
    }
  }

  .order-summary {
    position: sticky;
    top: 2rem;

    .order-items {
      margin-bottom: 1rem;

      .order-item {
        padding: 0.75rem 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .item-image {
          width: 50px;
          height: 50px;
          object-fit: cover;
          border-radius: 4px;
        }

        .item-name {
          font-size: 0.9rem;
          line-height: 1.3;
        }

        .item-variant {
          font-size: 0.8rem;
        }
      }
    }

    .coupon-section {
      margin: 1rem 0;

      .ant-input {
        border-radius: 6px 0 0 6px;
      }

      .ant-btn {
        border-radius: 0 6px 6px 0;
      }
    }

    .summary-calculations {
      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        &.total {
          margin-top: 0.5rem;
          padding-top: 0.5rem;
        }
      }
    }
  }

  .payment-methods {
    width: 100%;

    .payment-option {
      width: 100%;
      padding: 1rem;
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        border-color: #1890ff;
        background: #f6ffed;
      }

      .ant-radio {
        align-self: flex-start;
        margin-top: 0.2rem;
      }

      .payment-content {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        width: 100%;
        margin-left: 1.5rem;

        .payment-icon {
          font-size: 1.5rem;
          color: #1890ff;
          margin-top: 0.2rem;
        }
      }

      &.ant-radio-wrapper-checked {
        border-color: #1890ff;
        background: #f6ffed;
      }
    }
  }

  .credit-card-form {
    margin-top: 1rem;
  }

  .checkout-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
  }

  // Mobile responsive
  @media (max-width: 768px) {
    .container {
      padding: 1rem 0.5rem;
    }

    .checkout-steps {
      margin: 1rem 0 2rem 0;

      .ant-steps-item-title {
        font-size: 0.85rem;
      }
    }

    .order-summary {
      position: static;
      margin-top: 1rem;
    }

    .payment-methods {
      .payment-option {
        .payment-content {
          flex-direction: column;
          gap: 0.5rem;

          .payment-icon {
            align-self: flex-start;
          }
        }
      }
    }

    .checkout-actions {
      justify-content: center;

      .ant-space {
        width: 100%;
        justify-content: space-between;

        .ant-btn {
          flex: 1;
          max-width: 120px;
        }
      }
    }
  }

  @media (max-width: 576px) {
    .order-summary {
      .order-items {
        .order-item {
          .ant-row {
            align-items: flex-start;

            .ant-col:last-child {
              text-align: left;
              margin-top: 0.5rem;
            }
          }
        }
      }
    }
  }
}

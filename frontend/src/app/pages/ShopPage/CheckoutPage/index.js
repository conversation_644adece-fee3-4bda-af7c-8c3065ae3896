import React, { useState, useEffect } from 'react';
import './CheckoutPage.scss';
import {
  Layout,
  Typography,
  Card,
  Row,
  Col,
  Form,
  Input,
  Select,
  Radio,
  Button,
  Steps,
  Divider,
  Space,
  message,
  Spin,
  Breadcrumb,
  Modal,
  Alert
} from 'antd';
import {
  CreditCardOutlined,
  BankOutlined,
  WalletOutlined,
  EnvironmentOutlined,
  UserOutlined,
  HomeOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@app/hooks/useAuth';
import { LINK } from '@link';
import { API } from '@api';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { Step } = Steps;

const CheckoutPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated, user } = useAuth();
  const [form] = Form.useForm();

  const [currentStep, setCurrentStep] = useState(0);
  const [orderItems, setOrderItems] = useState([]);
  const [userAddresses, setUserAddresses] = useState([]);
  const [selectedAddress, setSelectedAddress] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState('cod');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [couponCode, setCouponCode] = useState('');
  const [couponDiscount, setCouponDiscount] = useState(0);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth', {
        state: {
          from: location.pathname,
          message: 'Bạn cần đăng nhập để tiến hành thanh toán'
        }
      });
      return;
    }

    loadCheckoutData();
  }, [isAuthenticated, navigate, location]);

  const loadCheckoutData = async () => {
    setLoading(true);
    try {
      // Load cart items and user addresses
      const [cartData, addressesData] = await Promise.allSettled([
        loadCartItems(),
        loadUserAddresses()
      ]);

      if (cartData.status === 'fulfilled') {
        setOrderItems(cartData.value);
      }

      if (addressesData.status === 'fulfilled') {
        setUserAddresses(addressesData.value);
        // Set default address if available
        const defaultAddress = addressesData.value.find(addr => addr.isDefault);
        if (defaultAddress) {
          setSelectedAddress(defaultAddress);
          form.setFieldsValue({
            shippingAddress: defaultAddress._id
          });
        }
      }

      // Pre-fill user info
      if (user) {
        form.setFieldsValue({
          fullName: user.fullName,
          email: user.email,
          phone: user.phone
        });
      }
    } catch (error) {
      console.error('Error loading checkout data:', error);
      message.error('Có lỗi xảy ra khi tải dữ liệu');
    } finally {
      setLoading(false);
    }
  };

  const loadCartItems = async () => {
    // Mock API call - replace with actual API
    return [
      {
        _id: '1',
        quantity: 1,
        itemTotal: 29990000,
        variantId: {
          _id: 'v1',
          name: 'iPhone 15 Pro Max 256GB',
          price: 29990000,
          attributes: [
            { name: 'Dung lượng', value: '256GB' },
            { name: 'Màu sắc', value: 'Titan Tự Nhiên' }
          ],
          productId: {
            _id: 'p1',
            name: 'iPhone 15 Pro Max',
            images: [{ url: '/images/iphone-15-pro.jpg' }]
          }
        }
      },
      {
        _id: '2',
        quantity: 1,
        itemTotal: 6290000,
        variantId: {
          _id: 'v2',
          name: 'AirPods Pro 3',
          price: 6290000,
          attributes: [
            { name: 'Màu sắc', value: 'Trắng' }
          ],
          productId: {
            _id: 'p2',
            name: 'AirPods Pro 3',
            images: [{ url: '/images/airpods-pro-3.jpg' }]
          }
        }
      }
    ];
  };

  const loadUserAddresses = async () => {
    // Mock API call - replace with actual API
    return [
      {
        _id: 'addr1',
        fullName: 'Nguyễn Văn A',
        phone: '0123456789',
        address: '123 Đường ABC',
        ward: 'Phường XYZ',
        district: 'Quận 1',
        province: 'TP. Hồ Chí Minh',
        isDefault: true
      },
      {
        _id: 'addr2',
        fullName: 'Nguyễn Văn A',
        phone: '0123456789',
        address: '456 Đường DEF',
        ward: 'Phường UVW',
        district: 'Quận 2',
        province: 'TP. Hồ Chí Minh',
        isDefault: false
      }
    ];
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const calculateSubtotal = () => {
    return orderItems.reduce((total, item) => total + item.itemTotal, 0);
  };

  const calculateShipping = () => {
    const subtotal = calculateSubtotal();
    return subtotal >= 2000000 ? 0 : 50000;
  };

  const calculateDiscount = () => {
    return couponDiscount;
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateShipping() - calculateDiscount();
  };

  const applyCoupon = async () => {
    if (!couponCode.trim()) {
      message.warning('Vui lòng nhập mã giảm giá');
      return;
    }

    try {
      // Mock API call - replace with actual API
      console.log('Applying coupon:', couponCode);

      // Mock coupon validation
      if (couponCode === 'DISCOUNT10') {
        setCouponDiscount(calculateSubtotal() * 0.1); // 10% discount
        message.success('Áp dụng mã giảm giá thành công!');
      } else {
        message.error('Mã giảm giá không hợp lệ');
      }
    } catch (error) {
      console.error('Error applying coupon:', error);
      message.error('Có lỗi xảy ra khi áp dụng mã giảm giá');
    }
  };

  const handleNext = () => {
    form.validateFields().then(() => {
      setCurrentStep(currentStep + 1);
    }).catch(() => {
      message.error('Vui lòng điền đầy đủ thông tin bắt buộc');
    });
  };

  const handlePrevious = () => {
    setCurrentStep(currentStep - 1);
  };

  const handlePlaceOrder = () => {
    if (orderItems.length === 0) {
      message.error('Giỏ hàng của bạn đang trống');
      return;
    }

    form.validateFields().then((values) => {
      Modal.confirm({
        title: 'Xác nhận đặt hàng',
        content: `Bạn có chắc chắn muốn đặt hàng với tổng tiền ${formatPrice(calculateTotal())}?`,
        icon: <ExclamationCircleOutlined />,
        onOk: () => placeOrder(values),
        okText: 'Đặt hàng',
        cancelText: 'Hủy'
      });
    }).catch(() => {
      message.error('Vui lòng kiểm tra lại thông tin');
    });
  };

  const placeOrder = async (formValues) => {
    setSubmitting(true);
    try {
      const orderData = {
        shippingInfo: {
          ...formValues,
          address: selectedAddress
        },
        items: orderItems.map(item => ({
          variantId: item.variantId._id,
          quantity: item.quantity,
          price: item.variantId.price
        })),
        paymentMethod,
        subtotal: calculateSubtotal(),
        shipping: calculateShipping(),
        discount: calculateDiscount(),
        total: calculateTotal(),
        couponCode: couponCode || null
      };

      // Mock API call - replace with actual API
      console.log('Placing order:', orderData);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      const orderId = 'DH' + Date.now();
      message.success(`Đặt hàng thành công! Mã đơn hàng: #${orderId}`);

      // Navigate to order success page
      navigate(LINK.SHOP.ORDER_SUCCESS.format(orderId));
    } catch (error) {
      console.error('Error placing order:', error);
      message.error('Có lỗi xảy ra khi đặt hàng. Vui lòng thử lại.');
    } finally {
      setSubmitting(false);
    }
  };

  const getBreadcrumbItems = () => {
    return [
      {
        title: <HomeOutlined />,
        href: LINK.SHOP.HOME
      },
      {
        title: 'Giỏ hàng',
        href: LINK.SHOP.CART
      },
      {
        title: 'Thanh toán'
      }
    ];
  };

  const OrderSummary = () => (
    <Card className="order-summary" title="Thông tin đơn hàng">
      <div className="order-items">
        {orderItems.map(item => (
          <div key={item._id} className="order-item">
            <Row gutter={12} align="middle">
              <Col span={6}>
                <img
                  src={item.variantId.productId.images[0]?.url || '/placeholder-image.jpg'}
                  alt={item.variantId.productId.name}
                  className="item-image"
                />
              </Col>
              <Col span={12}>
                <Text strong className="item-name">{item.variantId.productId.name}</Text>
                <br />
                <Text type="secondary" className="item-variant">
                  {item.variantId.attributes.map(attr => `${attr.name}: ${attr.value}`).join(', ')}
                </Text>
                <br />
                <Text type="secondary">x{item.quantity}</Text>
              </Col>
              <Col span={6} style={{ textAlign: 'right' }}>
                <Text strong>{formatPrice(item.itemTotal)}</Text>
              </Col>
            </Row>
          </div>
        ))}
      </div>

      <Divider />

      {/* Coupon Section */}
      <div className="coupon-section">
        <Space.Compact style={{ width: '100%' }}>
          <Input
            placeholder="Nhập mã giảm giá"
            value={couponCode}
            onChange={(e) => setCouponCode(e.target.value)}
          />
          <Button type="primary" onClick={applyCoupon}>
            Áp dụng
          </Button>
        </Space.Compact>
      </div>

      <Divider />

      <div className="summary-calculations">
        <div className="summary-row">
          <Text>Tạm tính:</Text>
          <Text strong>{formatPrice(calculateSubtotal())}</Text>
        </div>
        <div className="summary-row">
          <Text>Phí vận chuyển:</Text>
          <Text strong>
            {calculateShipping() === 0 ? 'Miễn phí' : formatPrice(calculateShipping())}
          </Text>
        </div>
        {couponDiscount > 0 && (
          <div className="summary-row">
            <Text>Giảm giá:</Text>
            <Text strong style={{ color: '#52c41a' }}>
              -{formatPrice(calculateDiscount())}
            </Text>
          </div>
        )}
        <Divider />
        <div className="summary-row total">
          <Text strong style={{ fontSize: '1.1em' }}>Tổng cộng:</Text>
          <Text strong style={{ fontSize: '1.2em', color: '#ff4d4f' }}>
            {formatPrice(calculateTotal())}
          </Text>
        </div>
      </div>
    </Card>
  );

  const ShippingForm = () => (
    <Card title={<><EnvironmentOutlined /> Thông tin giao hàng</>}>
      <Row gutter={16}>
        <Col xs={24} md={12}>
          <Form.Item
            name="fullName"
            label="Họ và tên"
            rules={[{ required: true, message: 'Vui lòng nhập họ và tên' }]}
          >
            <Input placeholder="Nhập họ và tên" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="phone"
            label="Số điện thoại"
            rules={[
              { required: true, message: 'Vui lòng nhập số điện thoại' },
              { pattern: /^[0-9]{10}$/, message: 'Số điện thoại không hợp lệ' }
            ]}
          >
            <Input placeholder="Nhập số điện thoại" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="email"
            label="Email"
            rules={[
              { required: true, message: 'Vui lòng nhập email' },
              { type: 'email', message: 'Email không hợp lệ' }
            ]}
          >
            <Input placeholder="Nhập email" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="city"
            label="Tỉnh/Thành phố"
            rules={[{ required: true, message: 'Vui lòng chọn tỉnh/thành phố' }]}
          >
            <Select placeholder="Chọn tỉnh/thành phố">
              <Option value="hanoi">Hà Nội</Option>
              <Option value="hcm">TP. Hồ Chí Minh</Option>
              <Option value="danang">Đà Nẵng</Option>
              <Option value="haiphong">Hải Phòng</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="district"
            label="Quận/Huyện"
            rules={[{ required: true, message: 'Vui lòng chọn quận/huyện' }]}
          >
            <Select placeholder="Chọn quận/huyện">
              <Option value="district1">Quận 1</Option>
              <Option value="district2">Quận 2</Option>
              <Option value="district3">Quận 3</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="ward"
            label="Phường/Xã"
            rules={[{ required: true, message: 'Vui lòng chọn phường/xã' }]}
          >
            <Select placeholder="Chọn phường/xã">
              <Option value="ward1">Phường 1</Option>
              <Option value="ward2">Phường 2</Option>
              <Option value="ward3">Phường 3</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item
            name="address"
            label="Địa chỉ cụ thể"
            rules={[{ required: true, message: 'Vui lòng nhập địa chỉ' }]}
          >
            <Input placeholder="Số nhà, tên đường..." />
          </Form.Item>
        </Col>
        <Col span={24}>
          <Form.Item name="note" label="Ghi chú">
            <TextArea
              placeholder="Ghi chú cho đơn hàng (tùy chọn)"
              rows={3}
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  const PaymentForm = () => (
    <Card title={<><CreditCardOutlined /> Phương thức thanh toán</>}>
      <Radio.Group
        value={paymentMethod}
        onChange={(e) => setPaymentMethod(e.target.value)}
        className="payment-methods"
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Radio value="cod" className="payment-option">
            <div className="payment-content">
              <WalletOutlined className="payment-icon" />
              <div>
                <Text strong>Thanh toán khi nhận hàng (COD)</Text>
                <br />
                <Text type="secondary">Thanh toán bằng tiền mặt khi nhận hàng</Text>
              </div>
            </div>
          </Radio>

          <Radio value="bank_transfer" className="payment-option">
            <div className="payment-content">
              <BankOutlined className="payment-icon" />
              <div>
                <Text strong>Chuyển khoản ngân hàng</Text>
                <br />
                <Text type="secondary">Chuyển khoản qua ngân hàng hoặc ví điện tử</Text>
              </div>
            </div>
          </Radio>

          <Radio value="credit_card" className="payment-option">
            <div className="payment-content">
              <CreditCardOutlined className="payment-icon" />
              <div>
                <Text strong>Thẻ tín dụng/Ghi nợ</Text>
                <br />
                <Text type="secondary">Visa, MasterCard, JCB</Text>
              </div>
            </div>
          </Radio>
        </Space>
      </Radio.Group>

      {paymentMethod === 'credit_card' && (
        <div className="credit-card-form">
          <Divider />
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="cardNumber"
                label="Số thẻ"
                rules={[{ required: true, message: 'Vui lòng nhập số thẻ' }]}
              >
                <Input placeholder="1234 5678 9012 3456" />
              </Form.Item>
            </Col>
            <Col xs={24} md={12}>
              <Form.Item
                name="cardName"
                label="Tên trên thẻ"
                rules={[{ required: true, message: 'Vui lòng nhập tên trên thẻ' }]}
              >
                <Input placeholder="Tên chủ thẻ" />
              </Form.Item>
            </Col>
            <Col xs={12} md={6}>
              <Form.Item
                name="expiry"
                label="Ngày hết hạn"
                rules={[{ required: true, message: 'Vui lòng nhập ngày hết hạn' }]}
              >
                <Input placeholder="MM/YY" />
              </Form.Item>
            </Col>
            <Col xs={12} md={6}>
              <Form.Item
                name="cvv"
                label="CVV"
                rules={[{ required: true, message: 'Vui lòng nhập CVV' }]}
              >
                <Input placeholder="123" />
              </Form.Item>
            </Col>
          </Row>
        </div>
      )}
    </Card>
  );

  const steps = [
    { title: 'Thông tin giao hàng', icon: <EnvironmentOutlined /> },
    { title: 'Thanh toán', icon: <CreditCardOutlined /> },
    { title: 'Xác nhận', icon: <UserOutlined /> }
  ];

  // Loading state
  if (loading) {
    return (
      <Layout className="checkout-page">
        <Content>
          <div className="loading-container">
            <Spin size="large" />
            <Text>Đang tải thông tin thanh toán...</Text>
          </div>
        </Content>
      </Layout>
    );
  }

  // Empty cart state
  if (orderItems.length === 0) {
    return (
      <Layout className="checkout-page">
        <Content>
          <div className="container">
            <Breadcrumb
              items={getBreadcrumbItems()}
              className="page-breadcrumb"
            />
            <Card className="empty-cart">
              <Empty
                image={<ShoppingCartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
                imageStyle={{ height: 80 }}
                description="Không có sản phẩm nào để thanh toán"
              >
                <Button
                  type="primary"
                  size="large"
                  onClick={() => navigate(LINK.SHOP.PRODUCTS)}
                >
                  Tiếp tục mua sắm
                </Button>
              </Empty>
            </Card>
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout className="checkout-page">
      <Content>
        <div className="container">
          <Breadcrumb
            items={getBreadcrumbItems()}
            className="page-breadcrumb"
          />

          <Title level={2}>Thanh toán</Title>

          <Steps current={currentStep} className="checkout-steps">
            {steps.map((step, index) => (
              <Step key={index} title={step.title} icon={step.icon} />
            ))}
          </Steps>

          <Form form={form} layout="vertical" className="checkout-form">
            <Row gutter={24}>
              <Col xs={24} lg={16}>
                {currentStep === 0 && <ShippingForm />}
                {currentStep === 1 && <PaymentForm />}
                {currentStep === 2 && (
                  <Card title="Xác nhận đơn hàng">
                    <Text>Vui lòng kiểm tra lại thông tin đơn hàng trước khi đặt hàng.</Text>
                  </Card>
                )}
              </Col>

              <Col xs={24} lg={8}>
                <OrderSummary />
              </Col>
            </Row>

            <div className="checkout-actions">
              <Space>
                {currentStep > 0 && (
                  <Button size="large" onClick={handlePrevious}>
                    Quay lại
                  </Button>
                )}
                {currentStep < steps.length - 1 && (
                  <Button type="primary" size="large" onClick={handleNext}>
                    Tiếp tục
                  </Button>
                )}
                {currentStep === steps.length - 1 && (
                  <Button
                    type="primary"
                    size="large"
                    onClick={handlePlaceOrder}
                    loading={submitting}
                    icon={<CheckCircleOutlined />}
                  >
                    Đặt hàng
                  </Button>
                )}
              </Space>
            </div>
          </Form>
        </div>
      </Content>
    </Layout>
  );
};

export default CheckoutPage;

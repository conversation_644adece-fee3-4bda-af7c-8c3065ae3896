.user-account-page {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    gap: 16px;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
  }

  .page-breadcrumb {
    margin-bottom: 1rem;
  }

  // User Sidebar
  .user-sidebar {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }

    .user-info {
      text-align: center;
      padding: 1.5rem 0;
      border-bottom: 1px solid #f0f0f0;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);

      .user-avatar {
        margin-bottom: 1rem;
      }

      .user-details {
        .user-name {
          margin-bottom: 0.5rem;
          color: #1890ff;
        }

        .user-email {
          display: block;
          margin-bottom: 0.5rem;
          font-size: 14px;
        }
      }
    }

    .user-menu {
      border: none;
      background: transparent;

      .ant-menu-item {
        margin: 0;
        border-radius: 8px;
        margin-bottom: 4px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          width: 3px;
          background: #1890ff;
          transform: scaleY(0);
          transition: transform 0.3s ease;
        }

        &:hover {
          background-color: #f0f8ff;
          transform: translateX(4px);
        }

        &.ant-menu-item-selected {
          background: linear-gradient(135deg, #e6f7ff 0%, #f0f8ff 100%);
          color: #1890ff;

          &::before {
            transform: scaleY(1);
          }

          &::after {
            display: none;
          }
        }

        .ant-menu-title-content {
          margin-left: 8px;
        }
      }
    }

    .logout-section {
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #f0f0f0;
    }
  }

  // Tab Content
  .tab-content {
    min-height: 500px;
  }

  // Profile Tab
  .profile-tab {
    .avatar-section {
      text-align: center;
      margin-bottom: 2rem;

      .profile-avatar {
        margin-bottom: 1rem;
      }

      .avatar-uploader {
        .ant-btn {
          margin-top: 0.5rem;
        }
      }
    }

    .form-actions {
      display: flex;
      gap: 8px;
    }

    .stat-item {
      text-align: center;
      padding: 1rem;
      border: 1px solid #f0f0f0;
      border-radius: 8px;

      .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 0.5rem;
      }

      .stat-label {
        color: #666;
        font-size: 14px;
      }
    }
  }

  // Orders Tab
  .orders-tab {
    .order-detail {
      .order-item {
        padding: 1rem 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }
      }
    }

    .review-form {
      .product-info {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
      }
    }
  }

  // Addresses Tab
  .addresses-tab {
    .address-card {
      border: 2px solid #f0f0f0;
      transition: all 0.3s ease;

      &:hover {
        border-color: #d9d9d9;
      }

      &.default-address {
        border-color: #1890ff;
        background-color: #f6ffed;
      }

      .address-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
      }

      .address-content {
        margin-bottom: 1rem;
        line-height: 1.6;
      }

      .address-actions {
        text-align: right;
      }
    }
  }

  // Wishlist Tab
  .wishlist-tab {
    .wishlist-item {
      .wishlist-item-footer {
        text-align: center;
        margin-top: 0.5rem;
      }
    }

    .stat-item {
      text-align: center;
      padding: 1rem;
      border: 1px solid #f0f0f0;
      border-radius: 8px;

      .stat-number {
        font-size: 1.5rem;
        font-weight: bold;
        color: #1890ff;
        margin-bottom: 0.5rem;
      }

      .stat-label {
        color: #666;
        font-size: 14px;
      }
    }
  }

  // Settings Tab
  .settings-tab {
    .notification-settings {
      .notification-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .notification-info {
          flex: 1;
        }
      }
    }
  }

  // Responsive
  @media (max-width: 768px) {
    .container {
      padding: 1rem 0.5rem;
    }

    .user-sidebar {
      margin-bottom: 1rem;

      .user-info {
        padding: 1rem 0;

        .user-avatar {
          width: 60px !important;
          height: 60px !important;
        }

        .user-name {
          font-size: 18px;
        }
      }

      .user-menu {
        .ant-menu-item {
          padding: 8px 16px;
          font-size: 14px;
        }
      }
    }

    .profile-tab {
      .avatar-section {
        .profile-avatar {
          width: 80px !important;
          height: 80px !important;
        }
      }

      .stat-item {
        padding: 0.75rem;

        .stat-number {
          font-size: 1.5rem;
        }

        .stat-label {
          font-size: 12px;
        }
      }
    }

    .addresses-tab {
      .address-card {
        margin-bottom: 1rem;
      }
    }

    .settings-tab {
      .notification-settings {
        .notification-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 0.5rem;

          .notification-info {
            margin-bottom: 0.5rem;
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 0.5rem;
    }

    .user-sidebar {
      .user-info {
        .user-name {
          font-size: 16px;
        }

        .user-email {
          font-size: 12px;
        }
      }
    }

    .profile-tab {
      .stat-item {
        .stat-number {
          font-size: 1.25rem;
        }
      }
    }
  }
}

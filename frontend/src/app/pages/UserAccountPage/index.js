import React, { useState, useEffect } from 'react';
import './UserAccountPage.scss';
import { 
  Layout, 
  Menu, 
  Card, 
  Row, 
  Col, 
  Typography, 
  Avatar, 
  Button, 
  Breadcrumb,
  Spin,
  message
} from 'antd';
import { 
  UserOutlined, 
  ShoppingOutlined, 
  EnvironmentOutlined, 
  HeartOutlined, 
  SettingOutlined,
  LogoutOutlined,
  HomeOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@app/hooks/useAuth';
import ProfileTab from './components/ProfileTab';
import OrdersTab from './components/OrdersTab';
import AddressesTab from './components/AddressesTab';
import WishlistTab from './components/WishlistTab';
import SettingsTab from './components/SettingsTab';
import { LINK } from '@link';

const { Content, Sider } = Layout;
const { Title, Text } = Typography;

const UserAccountPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout, isAuthenticated } = useAuth();
  
  const [selectedKey, setSelectedKey] = useState('profile');
  const [loading, setLoading] = useState(true);
  const [collapsed, setCollapsed] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/auth', { 
        state: { 
          from: location.pathname,
          message: 'Bạn cần đăng nhập để truy cập trang tài khoản'
        }
      });
      return;
    }

    // Get tab from URL
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get('tab') || 'profile';
    setSelectedKey(tab);
    
    setLoading(false);
  }, [isAuthenticated, navigate, location]);

  const handleMenuClick = ({ key }) => {
    setSelectedKey(key);
    navigate(`${LINK.USER_ACCOUNT}?tab=${key}`);
  };

  const handleLogout = () => {
    logout();
    message.success('Đăng xuất thành công');
    navigate(LINK.SHOP.HOME);
  };

  const getBreadcrumbItems = () => {
    const tabNames = {
      profile: 'Thông tin cá nhân',
      orders: 'Đơn hàng của tôi',
      addresses: 'Địa chỉ giao hàng',
      wishlist: 'Sản phẩm yêu thích',
      settings: 'Cài đặt tài khoản'
    };

    return [
      {
        title: <HomeOutlined />,
        href: LINK.SHOP.HOME
      },
      {
        title: 'Tài khoản'
      },
      {
        title: tabNames[selectedKey] || 'Thông tin cá nhân'
      }
    ];
  };

  const menuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: 'Thông tin cá nhân'
    },
    {
      key: 'orders',
      icon: <ShoppingOutlined />,
      label: 'Đơn hàng của tôi'
    },
    {
      key: 'addresses',
      icon: <EnvironmentOutlined />,
      label: 'Địa chỉ giao hàng'
    },
    {
      key: 'wishlist',
      icon: <HeartOutlined />,
      label: 'Sản phẩm yêu thích'
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Cài đặt tài khoản'
    }
  ];

  const renderTabContent = () => {
    switch (selectedKey) {
      case 'profile':
        return <ProfileTab />;
      case 'orders':
        return <OrdersTab />;
      case 'addresses':
        return <AddressesTab />;
      case 'wishlist':
        return <WishlistTab />;
      case 'settings':
        return <SettingsTab />;
      default:
        return <ProfileTab />;
    }
  };

  if (loading) {
    return (
      <Layout className="user-account-page">
        <Content>
          <div className="loading-container">
            <Spin size="large" />
            <Text>Đang tải thông tin tài khoản...</Text>
          </div>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout className="user-account-page">
      <Content>
        <div className="container">
          <Breadcrumb 
            items={getBreadcrumbItems()}
            className="page-breadcrumb"
          />

          <Row gutter={24}>
            {/* Sidebar */}
            <Col xs={24} md={6}>
              <Card className="user-sidebar">
                {/* User Info */}
                <div className="user-info">
                  <Avatar 
                    size={80} 
                    src={user?.avatar} 
                    icon={<UserOutlined />}
                    className="user-avatar"
                  />
                  <div className="user-details">
                    <Title level={4} className="user-name">
                      {user?.fullName || 'Người dùng'}
                    </Title>
                    <Text type="secondary" className="user-email">
                      {user?.email}
                    </Text>
                    <Button 
                      type="link" 
                      size="small" 
                      icon={<EditOutlined />}
                      onClick={() => setSelectedKey('profile')}
                    >
                      Chỉnh sửa
                    </Button>
                  </div>
                </div>

                {/* Menu */}
                <Menu
                  mode="vertical"
                  selectedKeys={[selectedKey]}
                  onClick={handleMenuClick}
                  className="user-menu"
                  items={menuItems}
                />

                {/* Logout */}
                <div className="logout-section">
                  <Button 
                    type="text" 
                    danger 
                    icon={<LogoutOutlined />}
                    onClick={handleLogout}
                    block
                  >
                    Đăng xuất
                  </Button>
                </div>
              </Card>
            </Col>

            {/* Main Content */}
            <Col xs={24} md={18}>
              <div className="tab-content">
                {renderTabContent()}
              </div>
            </Col>
          </Row>
        </div>
      </Content>
    </Layout>
  );
};

export default UserAccountPage;

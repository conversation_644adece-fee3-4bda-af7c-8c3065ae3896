import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Row, 
  Col, 
  Space, 
  Typography, 
  Tag,
  Popconfirm,
  message,
  Empty
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EnvironmentOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;

const AddressesTab = () => {
  const [addresses, setAddresses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadAddresses();
  }, []);

  const loadAddresses = async () => {
    setLoading(true);
    try {
      // Mock API call - replace with actual API
      const mockAddresses = [
        {
          _id: 'addr1',
          fullName: '<PERSON><PERSON><PERSON><PERSON>',
          phone: '0123456789',
          address: '123 Đường ABC',
          ward: 'Phường XYZ',
          district: 'Quận 1',
          province: 'TP. Hồ Chí Minh',
          isDefault: true
        },
        {
          _id: 'addr2',
          fullName: 'Nguyễn Văn A',
          phone: '0987654321',
          address: '456 Đường DEF',
          ward: 'Phường UVW',
          district: 'Quận 2',
          province: 'TP. Hồ Chí Minh',
          isDefault: false
        },
        {
          _id: 'addr3',
          fullName: 'Nguyễn Thị B',
          phone: '0123456789',
          address: '789 Đường GHI',
          ward: 'Phường RST',
          district: 'Quận 3',
          province: 'TP. Hồ Chí Minh',
          isDefault: false
        }
      ];

      setAddresses(mockAddresses);
    } catch (error) {
      console.error('Error loading addresses:', error);
      message.error('Có lỗi xảy ra khi tải danh sách địa chỉ');
    } finally {
      setLoading(false);
    }
  };

  const handleAddAddress = () => {
    setEditingAddress(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEditAddress = (address) => {
    setEditingAddress(address);
    form.setFieldsValue(address);
    setModalVisible(true);
  };

  const handleDeleteAddress = async (addressId) => {
    try {
      // Mock API call - replace with actual API
      console.log('Deleting address:', addressId);
      
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setAddresses(addresses.filter(addr => addr._id !== addressId));
      message.success('Đã xóa địa chỉ thành công!');
    } catch (error) {
      console.error('Error deleting address:', error);
      message.error('Có lỗi xảy ra khi xóa địa chỉ');
    }
  };

  const handleSetDefault = async (addressId) => {
    try {
      // Mock API call - replace with actual API
      console.log('Setting default address:', addressId);
      
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setAddresses(addresses.map(addr => ({
        ...addr,
        isDefault: addr._id === addressId
      })));
      
      message.success('Đã đặt làm địa chỉ mặc định!');
    } catch (error) {
      console.error('Error setting default address:', error);
      message.error('Có lỗi xảy ra khi đặt địa chỉ mặc định');
    }
  };

  const handleSaveAddress = async (values) => {
    try {
      console.log('Saving address:', values);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (editingAddress) {
        // Update existing address
        setAddresses(addresses.map(addr => 
          addr._id === editingAddress._id 
            ? { ...addr, ...values }
            : addr
        ));
        message.success('Cập nhật địa chỉ thành công!');
      } else {
        // Add new address
        const newAddress = {
          _id: 'addr' + Date.now(),
          ...values,
          isDefault: addresses.length === 0 // First address is default
        };
        setAddresses([...addresses, newAddress]);
        message.success('Thêm địa chỉ mới thành công!');
      }
      
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('Error saving address:', error);
      message.error('Có lỗi xảy ra khi lưu địa chỉ');
    }
  };

  const provinces = [
    'TP. Hồ Chí Minh',
    'Hà Nội',
    'Đà Nẵng',
    'Cần Thơ',
    'An Giang',
    'Bà Rịa - Vũng Tàu',
    'Bắc Giang',
    'Bắc Kạn',
    'Bạc Liêu'
  ];

  const districts = [
    'Quận 1',
    'Quận 2',
    'Quận 3',
    'Quận 4',
    'Quận 5',
    'Quận 6',
    'Quận 7',
    'Quận 8',
    'Quận 9',
    'Quận 10'
  ];

  const wards = [
    'Phường 1',
    'Phường 2',
    'Phường 3',
    'Phường 4',
    'Phường 5',
    'Phường ABC',
    'Phường XYZ',
    'Phường DEF'
  ];

  return (
    <div className="addresses-tab">
      <Card 
        title="Địa chỉ giao hàng"
        extra={
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleAddAddress}
          >
            Thêm địa chỉ mới
          </Button>
        }
      >
        {addresses.length > 0 ? (
          <Row gutter={[16, 16]}>
            {addresses.map((address) => (
              <Col xs={24} md={12} lg={8} key={address._id}>
                <Card 
                  className={`address-card ${address.isDefault ? 'default-address' : ''}`}
                  size="small"
                >
                  <div className="address-header">
                    <Text strong>{address.fullName}</Text>
                    {address.isDefault && (
                      <Tag color="blue" icon={<CheckCircleOutlined />}>
                        Mặc định
                      </Tag>
                    )}
                  </div>
                  
                  <div className="address-content">
                    <Text type="secondary">
                      <EnvironmentOutlined style={{ marginRight: 4 }} />
                      {address.phone}
                    </Text>
                    <br />
                    <Text>
                      {address.address}, {address.ward}, {address.district}, {address.province}
                    </Text>
                  </div>

                  <div className="address-actions">
                    <Space>
                      <Button 
                        type="link" 
                        size="small"
                        onClick={() => handleEditAddress(address)}
                      >
                        Sửa
                      </Button>
                      
                      {!address.isDefault && (
                        <Button 
                          type="link" 
                          size="small"
                          onClick={() => handleSetDefault(address._id)}
                        >
                          Đặt mặc định
                        </Button>
                      )}
                      
                      {addresses.length > 1 && (
                        <Popconfirm
                          title="Xóa địa chỉ"
                          description="Bạn có chắc chắn muốn xóa địa chỉ này?"
                          onConfirm={() => handleDeleteAddress(address._id)}
                          okText="Xóa"
                          cancelText="Hủy"
                        >
                          <Button 
                            type="link" 
                            danger 
                            size="small"
                          >
                            Xóa
                          </Button>
                        </Popconfirm>
                      )}
                    </Space>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        ) : (
          <Empty
            image={<EnvironmentOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
            description="Chưa có địa chỉ giao hàng nào"
          >
            <Button type="primary" onClick={handleAddAddress}>
              Thêm địa chỉ đầu tiên
            </Button>
          </Empty>
        )}
      </Card>

      {/* Add/Edit Address Modal */}
      <Modal
        title={editingAddress ? 'Sửa địa chỉ' : 'Thêm địa chỉ mới'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveAddress}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="fullName"
                label="Họ và tên"
                rules={[
                  { required: true, message: 'Vui lòng nhập họ và tên' }
                ]}
              >
                <Input placeholder="Nhập họ và tên" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="Số điện thoại"
                rules={[
                  { required: true, message: 'Vui lòng nhập số điện thoại' },
                  { pattern: /^[0-9]{10,11}$/, message: 'Số điện thoại không hợp lệ' }
                ]}
              >
                <Input placeholder="Nhập số điện thoại" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="Địa chỉ cụ thể"
            rules={[
              { required: true, message: 'Vui lòng nhập địa chỉ cụ thể' }
            ]}
          >
            <Input placeholder="Số nhà, tên đường..." />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="province"
                label="Tỉnh/Thành phố"
                rules={[
                  { required: true, message: 'Vui lòng chọn tỉnh/thành phố' }
                ]}
              >
                <Select placeholder="Chọn tỉnh/thành phố">
                  {provinces.map(province => (
                    <Option key={province} value={province}>
                      {province}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="district"
                label="Quận/Huyện"
                rules={[
                  { required: true, message: 'Vui lòng chọn quận/huyện' }
                ]}
              >
                <Select placeholder="Chọn quận/huyện">
                  {districts.map(district => (
                    <Option key={district} value={district}>
                      {district}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="ward"
                label="Phường/Xã"
                rules={[
                  { required: true, message: 'Vui lòng chọn phường/xã' }
                ]}
              >
                <Select placeholder="Chọn phường/xã">
                  {wards.map(ward => (
                    <Option key={ward} value={ward}>
                      {ward}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                Hủy
              </Button>
              <Button type="primary" htmlType="submit">
                {editingAddress ? 'Cập nhật' : 'Thêm địa chỉ'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AddressesTab;

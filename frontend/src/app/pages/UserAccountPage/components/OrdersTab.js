import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Tag, 
  Button, 
  Space, 
  Modal, 
  Typography, 
  Row, 
  Col, 
  Divider,
  Rate,
  Input,
  message,
  Empty,
  Tabs
} from 'antd';
import { 
  EyeOutlined, 
  StarOutlined, 
  ReloadOutlined,
  ShoppingCartOutlined,
  TruckOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { LINK } from '@link';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;

const OrdersTab = () => {
  const navigate = useNavigate();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [reviewVisible, setReviewVisible] = useState(false);
  const [reviewProduct, setReviewProduct] = useState(null);
  const [activeTab, setActiveTab] = useState('all');

  useEffect(() => {
    loadOrders();
  }, []);

  const loadOrders = async () => {
    setLoading(true);
    try {
      // Mock API call - replace with actual API
      const mockOrders = [
        {
          _id: 'DH001',
          orderNumber: 'DH001',
          createdAt: '2024-01-15T10:30:00Z',
          status: 'delivered',
          total: 36280000,
          items: [
            {
              _id: '1',
              quantity: 1,
              itemTotal: 29990000,
              variantId: {
                _id: 'v1',
                name: 'iPhone 15 Pro Max 256GB',
                price: 29990000,
                attributes: [
                  { name: 'Dung lượng', value: '256GB' },
                  { name: 'Màu sắc', value: 'Titan Tự Nhiên' }
                ],
                productId: {
                  _id: 'p1',
                  name: 'iPhone 15 Pro Max',
                  images: [{ url: '/images/iphone-15-pro.jpg' }]
                }
              },
              canReview: true
            },
            {
              _id: '2',
              quantity: 1,
              itemTotal: 6290000,
              variantId: {
                _id: 'v2',
                name: 'AirPods Pro 3',
                price: 6290000,
                attributes: [
                  { name: 'Màu sắc', value: 'Trắng' }
                ],
                productId: {
                  _id: 'p2',
                  name: 'AirPods Pro 3',
                  images: [{ url: '/images/airpods-pro-3.jpg' }]
                }
              },
              canReview: true
            }
          ],
          shippingAddress: {
            fullName: 'Nguyễn Văn A',
            phone: '**********',
            address: '123 Đường ABC, Phường XYZ, Quận 1, TP.HCM'
          },
          paymentMethod: 'cod',
          trackingNumber: 'TN123456789'
        },
        {
          _id: 'DH002',
          orderNumber: 'DH002',
          createdAt: '2024-01-20T14:20:00Z',
          status: 'shipping',
          total: 31990000,
          items: [
            {
              _id: '3',
              quantity: 1,
              itemTotal: 31990000,
              variantId: {
                _id: 'v3',
                name: 'MacBook Air M3 256GB',
                price: 31990000,
                attributes: [
                  { name: 'Dung lượng', value: '256GB' },
                  { name: 'RAM', value: '8GB' }
                ],
                productId: {
                  _id: 'p3',
                  name: 'MacBook Air M3',
                  images: [{ url: '/images/macbook-air-m3.jpg' }]
                }
              },
              canReview: false
            }
          ],
          shippingAddress: {
            fullName: 'Nguyễn Văn A',
            phone: '**********',
            address: '456 Đường DEF, Phường ABC, Quận 2, TP.HCM'
          },
          paymentMethod: 'bank_transfer',
          trackingNumber: 'TN987654321'
        },
        {
          _id: 'DH003',
          orderNumber: 'DH003',
          createdAt: '2024-01-22T09:15:00Z',
          status: 'processing',
          total: ********,
          items: [
            {
              _id: '4',
              quantity: 1,
              itemTotal: ********,
              variantId: {
                _id: 'v4',
                name: 'Samsung Galaxy S24 Ultra 512GB',
                price: ********,
                attributes: [
                  { name: 'Dung lượng', value: '512GB' },
                  { name: 'Màu sắc', value: 'Titanium Black' }
                ],
                productId: {
                  _id: 'p4',
                  name: 'Samsung Galaxy S24 Ultra',
                  images: [{ url: '/images/samsung-s24.jpg' }]
                }
              },
              canReview: false
            }
          ],
          shippingAddress: {
            fullName: 'Nguyễn Văn A',
            phone: '**********',
            address: '789 Đường GHI, Phường DEF, Quận 3, TP.HCM'
          },
          paymentMethod: 'credit_card',
          trackingNumber: null
        }
      ];

      setOrders(mockOrders);
    } catch (error) {
      console.error('Error loading orders:', error);
      message.error('Có lỗi xảy ra khi tải danh sách đơn hàng');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const statusColors = {
      pending: 'orange',
      processing: 'blue',
      shipping: 'cyan',
      delivered: 'green',
      cancelled: 'red'
    };
    return statusColors[status] || 'default';
  };

  const getStatusText = (status) => {
    const statusTexts = {
      pending: 'Chờ xử lý',
      processing: 'Đang xử lý',
      shipping: 'Đang giao',
      delivered: 'Đã giao',
      cancelled: 'Đã hủy'
    };
    return statusTexts[status] || status;
  };

  const getStatusIcon = (status) => {
    const statusIcons = {
      pending: <CloseCircleOutlined />,
      processing: <ReloadOutlined />,
      shipping: <TruckOutlined />,
      delivered: <CheckCircleOutlined />,
      cancelled: <CloseCircleOutlined />
    };
    return statusIcons[status] || <ReloadOutlined />;
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setDetailVisible(true);
  };

  const handleReorder = (order) => {
    // Add items to cart and navigate to cart
    message.success('Đã thêm sản phẩm vào giỏ hàng');
    navigate(LINK.SHOP.CART);
  };

  const handleReview = (product) => {
    setReviewProduct(product);
    setReviewVisible(true);
  };

  const handleSubmitReview = async (values) => {
    try {
      console.log('Submitting review:', values);
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('Đánh giá sản phẩm thành công!');
      setReviewVisible(false);
      setReviewProduct(null);
    } catch (error) {
      message.error('Có lỗi xảy ra khi gửi đánh giá');
    }
  };

  const getFilteredOrders = () => {
    if (activeTab === 'all') return orders;
    return orders.filter(order => order.status === activeTab);
  };

  const columns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: 'Ngày đặt',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => formatDate(date)
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'total',
      key: 'total',
      render: (total) => <Text strong>{formatPrice(total)}</Text>
    },
    {
      title: 'Thao tác',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button 
            type="primary" 
            size="small" 
            icon={<EyeOutlined />}
            onClick={() => handleViewOrder(record)}
          >
            Xem
          </Button>
          {record.status === 'delivered' && (
            <Button 
              size="small" 
              icon={<ShoppingCartOutlined />}
              onClick={() => handleReorder(record)}
            >
              Mua lại
            </Button>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="orders-tab">
      <Card title="Đơn hàng của tôi">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Tất cả" key="all" />
          <TabPane tab="Chờ xử lý" key="pending" />
          <TabPane tab="Đang xử lý" key="processing" />
          <TabPane tab="Đang giao" key="shipping" />
          <TabPane tab="Đã giao" key="delivered" />
          <TabPane tab="Đã hủy" key="cancelled" />
        </Tabs>

        {getFilteredOrders().length > 0 ? (
          <Table
            columns={columns}
            dataSource={getFilteredOrders()}
            rowKey="_id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `${range[0]}-${range[1]} của ${total} đơn hàng`
            }}
          />
        ) : (
          <Empty
            image={<ShoppingCartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
            description="Chưa có đơn hàng nào"
          >
            <Button type="primary" onClick={() => navigate(LINK.SHOP.PRODUCTS)}>
              Mua sắm ngay
            </Button>
          </Empty>
        )}
      </Card>

      {/* Order Detail Modal */}
      <Modal
        title={`Chi tiết đơn hàng ${selectedOrder?.orderNumber}`}
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div className="order-detail">
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>Ngày đặt hàng:</Text>
                <br />
                <Text>{formatDate(selectedOrder.createdAt)}</Text>
              </Col>
              <Col span={12}>
                <Text strong>Trạng thái:</Text>
                <br />
                <Tag color={getStatusColor(selectedOrder.status)} icon={getStatusIcon(selectedOrder.status)}>
                  {getStatusText(selectedOrder.status)}
                </Tag>
              </Col>
            </Row>

            <Divider />

            <Title level={5}>Sản phẩm</Title>
            {selectedOrder.items.map((item) => (
              <div key={item._id} className="order-item">
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <img 
                      src={item.variantId.productId.images[0]?.url} 
                      alt={item.variantId.productId.name}
                      style={{ width: '100%', borderRadius: 4 }}
                    />
                  </Col>
                  <Col span={12}>
                    <Text strong>{item.variantId.productId.name}</Text>
                    <br />
                    <Text type="secondary">
                      {item.variantId.attributes.map(attr => `${attr.name}: ${attr.value}`).join(', ')}
                    </Text>
                    <br />
                    <Text>Số lượng: {item.quantity}</Text>
                  </Col>
                  <Col span={6}>
                    <Text strong>{formatPrice(item.itemTotal)}</Text>
                  </Col>
                  <Col span={2}>
                    {item.canReview && selectedOrder.status === 'delivered' && (
                      <Button 
                        type="link" 
                        icon={<StarOutlined />}
                        onClick={() => handleReview(item)}
                        size="small"
                      >
                        Đánh giá
                      </Button>
                    )}
                  </Col>
                </Row>
              </div>
            ))}

            <Divider />

            <Row justify="end">
              <Col>
                <Text strong style={{ fontSize: 16 }}>
                  Tổng cộng: {formatPrice(selectedOrder.total)}
                </Text>
              </Col>
            </Row>
          </div>
        )}
      </Modal>

      {/* Review Modal */}
      <Modal
        title="Đánh giá sản phẩm"
        open={reviewVisible}
        onCancel={() => setReviewVisible(false)}
        footer={null}
      >
        {reviewProduct && (
          <div className="review-form">
            <div className="product-info">
              <img 
                src={reviewProduct.variantId.productId.images[0]?.url} 
                alt={reviewProduct.variantId.productId.name}
                style={{ width: 80, height: 80, borderRadius: 4 }}
              />
              <div style={{ marginLeft: 12 }}>
                <Text strong>{reviewProduct.variantId.productId.name}</Text>
                <br />
                <Text type="secondary">
                  {reviewProduct.variantId.attributes.map(attr => `${attr.name}: ${attr.value}`).join(', ')}
                </Text>
              </div>
            </div>

            <div style={{ marginTop: 16 }}>
              <Text strong>Đánh giá của bạn:</Text>
              <br />
              <Rate style={{ marginTop: 8 }} />
            </div>

            <div style={{ marginTop: 16 }}>
              <Text strong>Nhận xét:</Text>
              <TextArea 
                rows={4} 
                placeholder="Chia sẻ trải nghiệm của bạn về sản phẩm này..."
                style={{ marginTop: 8 }}
              />
            </div>

            <div style={{ marginTop: 16, textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setReviewVisible(false)}>
                  Hủy
                </Button>
                <Button type="primary" onClick={handleSubmitReview}>
                  Gửi đánh giá
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrdersTab;

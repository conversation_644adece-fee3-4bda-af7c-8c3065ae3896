import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Row, 
  Col, 
  Avatar, 
  Upload, 
  Typography, 
  DatePicker, 
  Select,
  message
} from 'antd';
import { 
  UserOutlined, 
  MailOutlined, 
  PhoneOutlined, 
  UploadOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined
} from '@ant-design/icons';
import { useAuth } from '@app/hooks/useAuth';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

const ProfileTab = () => {
  const { user, updateProfile } = useAuth();
  const [form] = Form.useForm();
  const [editing, setEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [avatarLoading, setAvatarLoading] = useState(false);

  useEffect(() => {
    if (user) {
      form.setFieldsValue({
        fullName: user.fullName,
        email: user.email,
        phone: user.phone,
        dateOfBirth: user.dateOfBirth ? dayjs(user.dateOfBirth) : null,
        gender: user.gender,
        address: user.address
      });
    }
  }, [user, form]);

  const handleSave = async (values) => {
    setLoading(true);
    try {
      console.log('Updating profile:', values);
      
      const updatedData = {
        ...values,
        dateOfBirth: values.dateOfBirth ? values.dateOfBirth.format('YYYY-MM-DD') : null
      };
      
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      if (updateProfile) {
        await updateProfile(updatedData);
      }
      
      message.success('Cập nhật thông tin thành công!');
      setEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
      message.error('Có lỗi xảy ra khi cập nhật thông tin');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setEditing(false);
  };

  const handleAvatarChange = async (info) => {
    if (info.file.status === 'uploading') {
      setAvatarLoading(true);
      return;
    }
    
    if (info.file.status === 'done') {
      setAvatarLoading(false);
      message.success('Cập nhật ảnh đại diện thành công!');
    } else if (info.file.status === 'error') {
      setAvatarLoading(false);
      message.error('Có lỗi xảy ra khi tải ảnh lên');
    }
  };

  const beforeUpload = (file) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('Chỉ có thể tải lên file JPG/PNG!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Kích thước ảnh phải nhỏ hơn 2MB!');
      return false;
    }
    return true;
  };

  return (
    <div className="profile-tab">
      <Card 
        title="Thông tin cá nhân"
        extra={
          !editing ? (
            <Button 
              type="primary" 
              icon={<EditOutlined />}
              onClick={() => setEditing(true)}
            >
              Chỉnh sửa
            </Button>
          ) : null
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          disabled={!editing}
        >
          <div className="avatar-section">
            <Avatar 
              size={120} 
              src={user?.avatar} 
              icon={<UserOutlined />}
              className="profile-avatar"
            />
            {editing && (
              <Upload
                name="avatar"
                listType="picture"
                className="avatar-uploader"
                showUploadList={false}
                action="/api/upload/avatar"
                beforeUpload={beforeUpload}
                onChange={handleAvatarChange}
              >
                <Button 
                  icon={<UploadOutlined />} 
                  loading={avatarLoading}
                  size="small"
                >
                  Thay đổi ảnh
                </Button>
              </Upload>
            )}
          </div>

          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                name="fullName"
                label="Họ và tên"
                rules={[
                  { required: true, message: 'Vui lòng nhập họ và tên' },
                  { min: 2, message: 'Họ và tên phải có ít nhất 2 ký tự' }
                ]}
              >
                <Input 
                  prefix={<UserOutlined />} 
                  placeholder="Nhập họ và tên"
                />
              </Form.Item>
            </Col>
            
            <Col xs={24} md={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Vui lòng nhập email' },
                  { type: 'email', message: 'Email không hợp lệ' }
                ]}
              >
                <Input 
                  prefix={<MailOutlined />} 
                  placeholder="Nhập email"
                  disabled
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                name="phone"
                label="Số điện thoại"
                rules={[
                  { required: true, message: 'Vui lòng nhập số điện thoại' },
                  { pattern: /^[0-9]{10,11}$/, message: 'Số điện thoại không hợp lệ' }
                ]}
              >
                <Input 
                  prefix={<PhoneOutlined />} 
                  placeholder="Nhập số điện thoại"
                />
              </Form.Item>
            </Col>
            
            <Col xs={24} md={12}>
              <Form.Item
                name="dateOfBirth"
                label="Ngày sinh"
              >
                <DatePicker 
                  placeholder="Chọn ngày sinh"
                  style={{ width: '100%' }}
                  format="DD/MM/YYYY"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col xs={24} md={12}>
              <Form.Item
                name="gender"
                label="Giới tính"
              >
                <Select placeholder="Chọn giới tính">
                  <Option value="male">Nam</Option>
                  <Option value="female">Nữ</Option>
                  <Option value="other">Khác</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="Địa chỉ"
          >
            <Input.TextArea 
              rows={3}
              placeholder="Nhập địa chỉ của bạn"
            />
          </Form.Item>

          {editing && (
            <Form.Item>
              <div className="form-actions">
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  Lưu thay đổi
                </Button>
                <Button 
                  onClick={handleCancel}
                  icon={<CloseOutlined />}
                  style={{ marginLeft: 8 }}
                >
                  Hủy
                </Button>
              </div>
            </Form.Item>
          )}
        </Form>
      </Card>

      <Card title="Thống kê tài khoản" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col xs={12} md={6}>
            <div className="stat-item">
              <div className="stat-number">12</div>
              <div className="stat-label">Đơn hàng</div>
            </div>
          </Col>
          <Col xs={12} md={6}>
            <div className="stat-item">
              <div className="stat-number">8</div>
              <div className="stat-label">Đã giao</div>
            </div>
          </Col>
          <Col xs={12} md={6}>
            <div className="stat-item">
              <div className="stat-number">25</div>
              <div className="stat-label">Yêu thích</div>
            </div>
          </Col>
          <Col xs={12} md={6}>
            <div className="stat-item">
              <div className="stat-number">156</div>
              <div className="stat-label">Điểm tích lũy</div>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default ProfileTab;

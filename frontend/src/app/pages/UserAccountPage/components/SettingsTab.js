import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Switch, 
  Typography, 
  Divider, 
  Space,
  Modal,
  message,
  Row,
  Col
} from 'antd';
import { 
  LockOutlined, 
  MailOutlined, 
  BellOutlined, 
  EyeInvisibleOutlined,
  EyeTwoTone,
  ExclamationCircleOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useAuth } from '@app/hooks/useAuth';

const { Title, Text } = Typography;

const SettingsTab = () => {
  const { user, logout } = useAuth();
  const [passwordForm] = Form.useForm();
  const [notificationForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [notifications, setNotifications] = useState({
    emailOrders: true,
    emailPromotions: false,
    emailNewsletter: true,
    pushOrders: true,
    pushPromotions: false,
    smsOrders: false,
    smsPromotions: false
  });

  const handleChangePassword = async (values) => {
    setLoading(true);
    try {
      console.log('Changing password:', values);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      message.success('Đổi mật khẩu thành công!');
      passwordForm.resetFields();
    } catch (error) {
      console.error('Error changing password:', error);
      message.error('Có lỗi xảy ra khi đổi mật khẩu');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationChange = async (key, value) => {
    try {
      const newNotifications = { ...notifications, [key]: value };
      setNotifications(newNotifications);
      
      console.log('Updating notification settings:', newNotifications);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      message.success('Cập nhật cài đặt thông báo thành công!');
    } catch (error) {
      console.error('Error updating notification settings:', error);
      message.error('Có lỗi xảy ra khi cập nhật cài đặt');
    }
  };

  const handleDeleteAccount = () => {
    Modal.confirm({
      title: 'Xóa tài khoản',
      icon: <ExclamationCircleOutlined />,
      content: (
        <div>
          <p>Bạn có chắc chắn muốn xóa tài khoản này?</p>
          <p><strong>Lưu ý:</strong> Hành động này không thể hoàn tác và sẽ xóa vĩnh viễn:</p>
          <ul>
            <li>Tất cả thông tin cá nhân</li>
            <li>Lịch sử đơn hàng</li>
            <li>Danh sách yêu thích</li>
            <li>Địa chỉ giao hàng</li>
          </ul>
        </div>
      ),
      okText: 'Xóa tài khoản',
      okType: 'danger',
      cancelText: 'Hủy',
      onOk: async () => {
        try {
          console.log('Deleting account');
          
          // Mock API call
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          message.success('Tài khoản đã được xóa thành công');
          logout();
        } catch (error) {
          console.error('Error deleting account:', error);
          message.error('Có lỗi xảy ra khi xóa tài khoản');
        }
      }
    });
  };

  const handleExportData = async () => {
    try {
      console.log('Exporting user data');
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock download
      const data = {
        user: user,
        exportDate: new Date().toISOString(),
        orders: [],
        addresses: [],
        wishlist: []
      };
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `user-data-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      message.success('Xuất dữ liệu thành công!');
    } catch (error) {
      console.error('Error exporting data:', error);
      message.error('Có lỗi xảy ra khi xuất dữ liệu');
    }
  };

  return (
    <div className="settings-tab">
      {/* Change Password */}
      <Card title="Đổi mật khẩu" style={{ marginBottom: 16 }}>
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleChangePassword}
        >
          <Form.Item
            name="currentPassword"
            label="Mật khẩu hiện tại"
            rules={[
              { required: true, message: 'Vui lòng nhập mật khẩu hiện tại' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="Nhập mật khẩu hiện tại"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="newPassword"
                label="Mật khẩu mới"
                rules={[
                  { required: true, message: 'Vui lòng nhập mật khẩu mới' },
                  { min: 6, message: 'Mật khẩu phải có ít nhất 6 ký tự' }
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="Nhập mật khẩu mới"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="confirmPassword"
                label="Xác nhận mật khẩu mới"
                dependencies={['newPassword']}
                rules={[
                  { required: true, message: 'Vui lòng xác nhận mật khẩu mới' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('Mật khẩu xác nhận không khớp'));
                    },
                  }),
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="Xác nhận mật khẩu mới"
                  iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              Đổi mật khẩu
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* Notification Settings */}
      <Card title="Cài đặt thông báo" style={{ marginBottom: 16 }}>
        <div className="notification-settings">
          <Title level={5}>Email</Title>
          <div className="notification-item">
            <div className="notification-info">
              <Text strong>Thông báo đơn hàng</Text>
              <br />
              <Text type="secondary">Nhận email về trạng thái đơn hàng</Text>
            </div>
            <Switch
              checked={notifications.emailOrders}
              onChange={(checked) => handleNotificationChange('emailOrders', checked)}
            />
          </div>
          
          <div className="notification-item">
            <div className="notification-info">
              <Text strong>Khuyến mãi</Text>
              <br />
              <Text type="secondary">Nhận email về các chương trình khuyến mãi</Text>
            </div>
            <Switch
              checked={notifications.emailPromotions}
              onChange={(checked) => handleNotificationChange('emailPromotions', checked)}
            />
          </div>
          
          <div className="notification-item">
            <div className="notification-info">
              <Text strong>Bản tin</Text>
              <br />
              <Text type="secondary">Nhận bản tin về sản phẩm mới và tin tức</Text>
            </div>
            <Switch
              checked={notifications.emailNewsletter}
              onChange={(checked) => handleNotificationChange('emailNewsletter', checked)}
            />
          </div>

          <Divider />

          <Title level={5}>Thông báo đẩy</Title>
          <div className="notification-item">
            <div className="notification-info">
              <Text strong>Thông báo đơn hàng</Text>
              <br />
              <Text type="secondary">Nhận thông báo đẩy về trạng thái đơn hàng</Text>
            </div>
            <Switch
              checked={notifications.pushOrders}
              onChange={(checked) => handleNotificationChange('pushOrders', checked)}
            />
          </div>
          
          <div className="notification-item">
            <div className="notification-info">
              <Text strong>Khuyến mãi</Text>
              <br />
              <Text type="secondary">Nhận thông báo đẩy về các chương trình khuyến mãi</Text>
            </div>
            <Switch
              checked={notifications.pushPromotions}
              onChange={(checked) => handleNotificationChange('pushPromotions', checked)}
            />
          </div>

          <Divider />

          <Title level={5}>SMS</Title>
          <div className="notification-item">
            <div className="notification-info">
              <Text strong>Thông báo đơn hàng</Text>
              <br />
              <Text type="secondary">Nhận SMS về trạng thái đơn hàng</Text>
            </div>
            <Switch
              checked={notifications.smsOrders}
              onChange={(checked) => handleNotificationChange('smsOrders', checked)}
            />
          </div>
          
          <div className="notification-item">
            <div className="notification-info">
              <Text strong>Khuyến mãi</Text>
              <br />
              <Text type="secondary">Nhận SMS về các chương trình khuyến mãi</Text>
            </div>
            <Switch
              checked={notifications.smsPromotions}
              onChange={(checked) => handleNotificationChange('smsPromotions', checked)}
            />
          </div>
        </div>
      </Card>

      {/* Privacy & Data */}
      <Card title="Quyền riêng tư & Dữ liệu" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Title level={5}>Xuất dữ liệu</Title>
            <Text type="secondary">
              Tải xuống bản sao dữ liệu cá nhân của bạn
            </Text>
            <br />
            <Button onClick={handleExportData} style={{ marginTop: 8 }}>
              Xuất dữ liệu
            </Button>
          </div>

          <Divider />

          <div>
            <Title level={5} type="danger">Xóa tài khoản</Title>
            <Text type="secondary">
              Xóa vĩnh viễn tài khoản và tất cả dữ liệu liên quan
            </Text>
            <br />
            <Button 
              danger 
              icon={<DeleteOutlined />}
              onClick={handleDeleteAccount}
              style={{ marginTop: 8 }}
            >
              Xóa tài khoản
            </Button>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default SettingsTab;

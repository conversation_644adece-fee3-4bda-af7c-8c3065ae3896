import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Button, 
  Typography, 
  message,
  Empty,
  Spin
} from 'antd';
import { 
  HeartOutlined, 
  ShoppingCartOutlined, 
  DeleteOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import ProductCard from '@app/components/ProductCard';
import { LINK } from '@link';

const { Title, Text } = Typography;

const WishlistTab = () => {
  const navigate = useNavigate();
  const [wishlistItems, setWishlistItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadWishlist();
  }, []);

  const loadWishlist = async () => {
    setLoading(true);
    try {
      // Mock API call - replace with actual API
      const mockWishlistItems = [
        {
          _id: '1',
          name: 'iPhone 15 Pro Max',
          price: 29990000,
          originalPrice: 32990000,
          images: [{ url: '/images/iphone-15-pro.jpg' }],
          averageRating: 4.8,
          totalReviews: 156,
          isFeatured: true,
          totalStock: 50,
          addedAt: '2024-01-15T10:30:00Z'
        },
        {
          _id: '2',
          name: 'Samsung Galaxy S24 Ultra',
          price: 27490000,
          originalPrice: 29990000,
          images: [{ url: '/images/samsung-s24.jpg' }],
          averageRating: 4.7,
          totalReviews: 89,
          isFeatured: true,
          totalStock: 30,
          addedAt: '2024-01-18T14:20:00Z'
        },
        {
          _id: '3',
          name: 'MacBook Air M3',
          price: 31990000,
          originalPrice: 34990000,
          images: [{ url: '/images/macbook-air-m3.jpg' }],
          averageRating: 4.9,
          totalReviews: 234,
          isFeatured: true,
          totalStock: 20,
          addedAt: '2024-01-20T09:15:00Z'
        },
        {
          _id: '4',
          name: 'iPad Pro 13-inch M4',
          price: 32990000,
          images: [{ url: '/images/ipad-pro-m4.jpg' }],
          averageRating: 4.8,
          totalReviews: 67,
          isNew: true,
          totalStock: 25,
          addedAt: '2024-01-22T16:45:00Z'
        },
        {
          _id: '5',
          name: 'AirPods Pro 3',
          price: 6290000,
          images: [{ url: '/images/airpods-pro-3.jpg' }],
          averageRating: 4.6,
          totalReviews: 123,
          isNew: true,
          totalStock: 100,
          addedAt: '2024-01-25T11:30:00Z'
        },
        {
          _id: '6',
          name: 'Sony WH-1000XM5',
          price: 8990000,
          images: [{ url: '/images/sony-headphones.jpg' }],
          averageRating: 4.7,
          totalReviews: 89,
          totalStock: 0, // Out of stock
          addedAt: '2024-01-28T13:20:00Z'
        }
      ];

      setWishlistItems(mockWishlistItems);
    } catch (error) {
      console.error('Error loading wishlist:', error);
      message.error('Có lỗi xảy ra khi tải danh sách yêu thích');
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (product) => {
    try {
      console.log('Adding to cart:', product);
      message.success('Đã thêm sản phẩm vào giỏ hàng!');
    } catch (error) {
      console.error('Error adding to cart:', error);
      message.error('Có lỗi xảy ra khi thêm vào giỏ hàng');
    }
  };

  const handleRemoveFromWishlist = async (product) => {
    try {
      console.log('Removing from wishlist:', product);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setWishlistItems(wishlistItems.filter(item => item._id !== product._id));
      message.success('Đã xóa khỏi danh sách yêu thích!');
    } catch (error) {
      console.error('Error removing from wishlist:', error);
      message.error('Có lỗi xảy ra khi xóa khỏi danh sách yêu thích');
    }
  };

  const handleAddAllToCart = async () => {
    try {
      const availableItems = wishlistItems.filter(item => item.totalStock > 0);
      
      if (availableItems.length === 0) {
        message.warning('Không có sản phẩm nào còn hàng để thêm vào giỏ');
        return;
      }

      console.log('Adding all available items to cart:', availableItems);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      message.success(`Đã thêm ${availableItems.length} sản phẩm vào giỏ hàng!`);
      navigate(LINK.SHOP.CART);
    } catch (error) {
      console.error('Error adding all to cart:', error);
      message.error('Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng');
    }
  };

  const handleClearWishlist = async () => {
    try {
      console.log('Clearing wishlist');
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setWishlistItems([]);
      message.success('Đã xóa tất cả sản phẩm khỏi danh sách yêu thích!');
    } catch (error) {
      console.error('Error clearing wishlist:', error);
      message.error('Có lỗi xảy ra khi xóa danh sách yêu thích');
    }
  };

  const isInWishlist = (product) => {
    return wishlistItems.some(item => item._id === product._id);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="wishlist-tab">
        <Card title="Sản phẩm yêu thích">
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <Spin size="large" />
            <div style={{ marginTop: '1rem' }}>
              <Text>Đang tải danh sách yêu thích...</Text>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="wishlist-tab">
      <Card 
        title={`Sản phẩm yêu thích (${wishlistItems.length})`}
        extra={
          wishlistItems.length > 0 && (
            <div>
              <Button 
                type="primary" 
                icon={<ShoppingCartOutlined />}
                onClick={handleAddAllToCart}
                style={{ marginRight: 8 }}
              >
                Thêm tất cả vào giỏ
              </Button>
              <Button 
                danger 
                icon={<DeleteOutlined />}
                onClick={handleClearWishlist}
              >
                Xóa tất cả
              </Button>
            </div>
          )
        }
      >
        {wishlistItems.length > 0 ? (
          <Row gutter={[16, 16]}>
            {wishlistItems.map((product) => (
              <Col xs={24} sm={12} md={8} lg={6} key={product._id}>
                <div className="wishlist-item">
                  <ProductCard 
                    product={product}
                    onAddToCart={handleAddToCart}
                    onToggleWishlist={handleRemoveFromWishlist}
                    isInWishlist={isInWishlist(product)}
                  />
                  <div className="wishlist-item-footer">
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      Đã thêm: {formatDate(product.addedAt)}
                    </Text>
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        ) : (
          <Empty
            image={<HeartOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
            description="Chưa có sản phẩm yêu thích nào"
          >
            <Button type="primary" onClick={() => navigate(LINK.SHOP.PRODUCTS)}>
              Khám phá sản phẩm
            </Button>
          </Empty>
        )}
      </Card>

      {/* Wishlist Statistics */}
      {wishlistItems.length > 0 && (
        <Card title="Thống kê" style={{ marginTop: 16 }}>
          <Row gutter={16}>
            <Col xs={12} md={6}>
              <div className="stat-item">
                <div className="stat-number">{wishlistItems.length}</div>
                <div className="stat-label">Tổng sản phẩm</div>
              </div>
            </Col>
            <Col xs={12} md={6}>
              <div className="stat-item">
                <div className="stat-number">
                  {wishlistItems.filter(item => item.totalStock > 0).length}
                </div>
                <div className="stat-label">Còn hàng</div>
              </div>
            </Col>
            <Col xs={12} md={6}>
              <div className="stat-item">
                <div className="stat-number">
                  {wishlistItems.filter(item => item.totalStock === 0).length}
                </div>
                <div className="stat-label">Hết hàng</div>
              </div>
            </Col>
            <Col xs={12} md={6}>
              <div className="stat-item">
                <div className="stat-number">
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND',
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0
                  }).format(
                    wishlistItems.reduce((total, item) => total + item.price, 0)
                  )}
                </div>
                <div className="stat-label">Tổng giá trị</div>
              </div>
            </Col>
          </Row>
        </Card>
      )}
    </div>
  );
};

export default WishlistTab;

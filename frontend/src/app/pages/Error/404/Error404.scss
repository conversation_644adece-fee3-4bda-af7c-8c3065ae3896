// Error 404 Page Styles
.error-404-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .ant-layout-content {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem 1rem;
  }

  .error-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }

  .error-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 3rem 2rem;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    @media (max-width: 768px) {
      padding: 2rem 1.5rem;
      border-radius: 16px;
    }

    @media (max-width: 480px) {
      padding: 1.5rem 1rem;
      border-radius: 12px;
    }
  }

  .error-illustration {
    margin-bottom: 2rem;

    .error-number {
      font-size: 8rem;
      font-weight: 900;
      background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1;
      margin-bottom: 1rem;

      @media (max-width: 768px) {
        font-size: 6rem;
      }

      @media (max-width: 480px) {
        font-size: 4rem;
      }
    }

    .error-icon {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;

      svg {
        max-width: 200px;
        height: auto;

        @media (max-width: 768px) {
          max-width: 150px;
        }

        @media (max-width: 480px) {
          max-width: 120px;
        }
      }
    }
  }

  .error-text {
    margin-bottom: 2.5rem;

    .error-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #262626;
      margin-bottom: 1rem;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }

      @media (max-width: 480px) {
        font-size: 1.5rem;
      }
    }

    .error-description {
      font-size: 1.125rem;
      color: #595959;
      line-height: 1.6;
      margin-bottom: 0;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 768px) {
        font-size: 1rem;
      }

      @media (max-width: 480px) {
        font-size: 0.875rem;
      }
    }
  }

  .error-actions {
    margin-bottom: 2rem;

    .ant-btn {
      border-radius: 8px;
      font-weight: 500;
      height: 48px;
      padding: 0 24px;
      font-size: 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      @media (max-width: 768px) {
        height: 44px;
        font-size: 15px;
        padding: 0 20px;
      }

      @media (max-width: 480px) {
        height: 40px;
        font-size: 14px;
        padding: 0 16px;
      }

      &.home-button {
        background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        border: none;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

        &:hover {
          background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
          box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
          transform: translateY(-2px);
        }

        &:active {
          transform: translateY(0);
        }
      }

      &.back-button {
        background: transparent;
        border: 2px solid #d9d9d9;
        color: #595959;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .ant-space {
      @media (max-width: 576px) {
        flex-direction: column;
        width: 100%;

        .ant-space-item {
          width: 100%;

          .ant-btn {
            width: 100%;
          }
        }
      }
    }
  }

  .error-footer {
    .ant-typography {
      color: #8c8c8c;
      font-size: 14px;
      margin-bottom: 0;

      a {
        color: #1890ff;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.3s ease;

        &:hover {
          color: #40a9ff;
          text-decoration: underline;
        }
      }

      @media (max-width: 480px) {
        font-size: 12px;
      }
    }
  }

  // Animation for the error illustration
  .error-illustration {
    animation: fadeInUp 0.8s ease-out forwards;
  }

  .error-text {
    animation: fadeInUp 0.8s ease-out 0.2s forwards;
    opacity: 0;
  }

  .error-actions {
    animation: fadeInUp 0.8s ease-out 0.4s forwards;
    opacity: 0;
  }

  .error-footer {
    animation: fadeInUp 0.8s ease-out 0.6s forwards;
    opacity: 0;
  }
}

// Keyframe animations
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .error-404-page {
    .error-content {
      background: rgba(26, 26, 26, 0.95);
      border: 1px solid rgba(255, 255, 255, 0.1);

      .error-title {
        color: #f0f0f0;
      }

      .error-description {
        color: #bfbfbf;
      }

      .back-button {
        border-color: #434343;
        color: #bfbfbf;

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }

      .error-footer .ant-typography {
        color: #8c8c8c;

        a {
          color: #1890ff;

          &:hover {
            color: #40a9ff;
          }
        }
      }
    }
  }
}

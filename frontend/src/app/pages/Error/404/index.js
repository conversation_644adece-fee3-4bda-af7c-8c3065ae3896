import React from 'react';
import { Layout, But<PERSON>, Typo<PERSON>, Space } from 'antd';
import { HomeOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { LINK } from '@link';
import './Error404.scss';

const { Content } = Layout;
const { Title, Paragraph } = Typography;

const Error404 = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate(LINK.SHOP.HOME);
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <Layout className="error-404-page">
      <Content>
        <div className="error-container">
          <div className="error-content">
            <div className="error-illustration">
              <div className="error-number">404</div>
              <div className="error-icon">
                <svg width="200" height="150" viewBox="0 0 200 150" fill="none">
                  <circle cx="100" cy="75" r="60" fill="#f0f2f5" stroke="#d9d9d9" strokeWidth="2"/>
                  <path d="M70 65 L130 85 M130 65 L70 85" stroke="#8c8c8c" strokeWidth="3" strokeLinecap="round"/>
                  <circle cx="100" cy="105" r="3" fill="#8c8c8c"/>
                </svg>
              </div>
            </div>
            
            <div className="error-text">
              <Title level={1} className="error-title">
                Trang không tìm thấy
              </Title>
              <Paragraph className="error-description">
                Xin lỗi, trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.
                Vui lòng kiểm tra lại đường dẫn hoặc quay về trang chủ.
              </Paragraph>
            </div>

            <div className="error-actions">
              <Space size="large">
                <Button 
                  type="primary" 
                  size="large"
                  icon={<HomeOutlined />}
                  onClick={handleGoHome}
                  className="home-button"
                >
                  Về trang chủ
                </Button>
                <Button 
                  size="large"
                  icon={<ArrowLeftOutlined />}
                  onClick={handleGoBack}
                  className="back-button"
                >
                  Quay lại
                </Button>
              </Space>
            </div>

            <div className="error-footer">
              <Paragraph type="secondary">
                Nếu bạn nghĩ đây là một lỗi, vui lòng{' '}
                <a href="mailto:<EMAIL>">liên hệ với chúng tôi</a>
              </Paragraph>
            </div>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default Error404;

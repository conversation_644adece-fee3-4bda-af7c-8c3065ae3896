import { useState, useEffect } from 'react';
import { Grid } from 'antd';

const { useBreakpoint } = Grid;

// Custom hook for responsive behavior
export const useResponsive = () => {
  const screens = useBreakpoint();
  
  const isMobile = screens.xs && !screens.sm;
  const isTablet = screens.sm && !screens.lg;
  const isDesktop = screens.lg;
  const isLargeDesktop = screens.xl;
  
  return {
    screens,
    isMobile,
    isTablet,
    isDesktop,
    isLargeDesktop,
    isSmallScreen: isMobile || isTablet,
    isLargeScreen: isDesktop || isLargeDesktop,
  };
};

// Hook for window dimensions
export const useWindowSize = () => {
  const [windowSize, setWindowSize] = useState({
    width: undefined,
    height: undefined,
  });

  useEffect(() => {
    function handleResize() {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    }

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return windowSize;
};

// Hook for media queries
export const useMediaQuery = (query) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const media = window.matchMedia(query);
    if (media.matches !== matches) {
      setMatches(media.matches);
    }
    
    const listener = () => setMatches(media.matches);
    media.addEventListener('change', listener);
    
    return () => media.removeEventListener('change', listener);
  }, [matches, query]);

  return matches;
};

// Hook for device detection
export const useDevice = () => {
  const [device, setDevice] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isTouchDevice: false,
    userAgent: '',
  });

  useEffect(() => {
    const userAgent = navigator.userAgent;
    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);
    const isDesktop = !isMobile && !isTablet;

    setDevice({
      isMobile: isMobile && !isTablet,
      isTablet,
      isDesktop,
      isTouchDevice,
      userAgent,
    });
  }, []);

  return device;
};

// Hook for scroll position
export const useScrollPosition = () => {
  const [scrollPosition, setScrollPosition] = useState({
    x: 0,
    y: 0,
  });

  useEffect(() => {
    const updatePosition = () => {
      setScrollPosition({
        x: window.pageXOffset,
        y: window.pageYOffset,
      });
    };

    window.addEventListener('scroll', updatePosition);
    updatePosition();

    return () => window.removeEventListener('scroll', updatePosition);
  }, []);

  return scrollPosition;
};

// Hook for viewport visibility
export const useViewportVisibility = () => {
  const [isVisible, setIsVisible] = useState(!document.hidden);

  useEffect(() => {
    const handleVisibilityChange = () => {
      setIsVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return isVisible;
};

// Hook for orientation
export const useOrientation = () => {
  const [orientation, setOrientation] = useState({
    angle: 0,
    type: 'landscape-primary',
  });

  useEffect(() => {
    const handleOrientationChange = () => {
      setOrientation({
        angle: window.screen?.orientation?.angle || 0,
        type: window.screen?.orientation?.type || 'landscape-primary',
      });
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    handleOrientationChange();

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, []);

  return orientation;
};

// Hook for responsive grid columns
export const useResponsiveColumns = (config = {}) => {
  const { screens } = useResponsive();
  
  const defaultConfig = {
    xs: 1,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 4,
    xxl: 5,
  };
  
  const finalConfig = { ...defaultConfig, ...config };
  
  if (screens.xxl) return finalConfig.xxl;
  if (screens.xl) return finalConfig.xl;
  if (screens.lg) return finalConfig.lg;
  if (screens.md) return finalConfig.md;
  if (screens.sm) return finalConfig.sm;
  return finalConfig.xs;
};

// Hook for responsive values
export const useResponsiveValue = (values) => {
  const { screens } = useResponsive();
  
  if (typeof values !== 'object') return values;
  
  if (screens.xxl && values.xxl !== undefined) return values.xxl;
  if (screens.xl && values.xl !== undefined) return values.xl;
  if (screens.lg && values.lg !== undefined) return values.lg;
  if (screens.md && values.md !== undefined) return values.md;
  if (screens.sm && values.sm !== undefined) return values.sm;
  if (screens.xs && values.xs !== undefined) return values.xs;
  
  return values.default || values;
};

export default useResponsive;

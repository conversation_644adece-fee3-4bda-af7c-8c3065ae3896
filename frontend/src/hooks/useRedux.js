import { useSelector, useDispatch } from 'react-redux';
import { useCallback } from 'react';
import { addToCart, updateCartItemQuantity, removeCartItem, clearCart } from '../store/slices/cartSlice';
import { addToWishlist, removeFromWishlist, toggleWishlist, clearWishlist } from '../store/slices/wishlistSlice';
import { addNotification, removeNotification, toggleSidebar, toggleMobileMenu, toggleCartDrawer, openQuickView, closeQuickView } from '../store/slices/uiSlice';

// Auth hooks
export const useAuth = () => {
  const dispatch = useDispatch();
  const auth = useSelector(state => state.auth);

  return {
    ...auth,
    dispatch,
  };
};

// Product hooks
export const useProducts = () => {
  const dispatch = useDispatch();
  const product = useSelector(state => state.product);

  return {
    ...product,
    dispatch,
  };
};

// Cart hooks
export const useCart = () => {
  const dispatch = useDispatch();
  const cart = useSelector(state => state.cart);

  const addToCartAction = useCallback((productData) => {
    return dispatch(addToCart(productData));
  }, [dispatch]);

  const updateQuantity = useCallback((cartItemId, quantity) => {
    return dispatch(updateCartItemQuantity({ cartItemId, quantity }));
  }, [dispatch]);

  const removeItem = useCallback((cartItemId) => {
    return dispatch(removeCartItem(cartItemId));
  }, [dispatch]);

  const clearCartAction = useCallback(() => {
    return dispatch(clearCart());
  }, [dispatch]);

  return {
    ...cart,
    addToCart: addToCartAction,
    updateQuantity,
    removeItem,
    clearCart: clearCartAction,
    dispatch,
  };
};

// Order hooks
export const useOrders = () => {
  const dispatch = useDispatch();
  const order = useSelector(state => state.order);

  return {
    ...order,
    dispatch,
  };
};

// Category hooks
export const useCategories = () => {
  const dispatch = useDispatch();
  const category = useSelector(state => state.category);

  return {
    ...category,
    dispatch,
  };
};

// Wishlist hooks
export const useWishlist = () => {
  const dispatch = useDispatch();
  const wishlist = useSelector(state => state.wishlist);

  const addToWishlistAction = useCallback((product) => {
    dispatch(addToWishlist(product));
  }, [dispatch]);

  const removeFromWishlistAction = useCallback((productId) => {
    dispatch(removeFromWishlist(productId));
  }, [dispatch]);

  const toggleWishlistAction = useCallback((product) => {
    dispatch(toggleWishlist(product));
  }, [dispatch]);

  const clearWishlistAction = useCallback(() => {
    dispatch(clearWishlist());
  }, [dispatch]);

  const isInWishlist = useCallback((productId) => {
    return wishlist.items.some(item => item._id === productId);
  }, [wishlist.items]);

  return {
    ...wishlist,
    addToWishlist: addToWishlistAction,
    removeFromWishlist: removeFromWishlistAction,
    toggleWishlist: toggleWishlistAction,
    clearWishlist: clearWishlistAction,
    isInWishlist,
    dispatch,
  };
};

// UI hooks
export const useUI = () => {
  const dispatch = useDispatch();
  const ui = useSelector(state => state.ui);

  const showNotification = useCallback((notification) => {
    dispatch(addNotification(notification));
  }, [dispatch]);

  const hideNotification = useCallback((id) => {
    dispatch(removeNotification(id));
  }, [dispatch]);

  const toggleSidebarAction = useCallback(() => {
    dispatch(toggleSidebar());
  }, [dispatch]);

  const toggleMobileMenuAction = useCallback(() => {
    dispatch(toggleMobileMenu());
  }, [dispatch]);

  const toggleCartDrawerAction = useCallback(() => {
    dispatch(toggleCartDrawer());
  }, [dispatch]);

  const openQuickViewAction = useCallback((product) => {
    dispatch(openQuickView(product));
  }, [dispatch]);

  const closeQuickViewAction = useCallback(() => {
    dispatch(closeQuickView());
  }, [dispatch]);

  return {
    ...ui,
    showNotification,
    hideNotification,
    toggleSidebar: toggleSidebarAction,
    toggleMobileMenu: toggleMobileMenuAction,
    toggleCartDrawer: toggleCartDrawerAction,
    openQuickView: openQuickViewAction,
    closeQuickView: closeQuickViewAction,
    dispatch,
  };
};

// Combined hooks for common use cases
export const useShop = () => {
  const auth = useAuth();
  const products = useProducts();
  const cart = useCart();
  const wishlist = useWishlist();
  const categories = useCategories();
  const ui = useUI();

  return {
    auth,
    products,
    cart,
    wishlist,
    categories,
    ui,
  };
};

// Generic Redux hooks
export const useAppSelector = useSelector;
export const useAppDispatch = () => useDispatch();

// Breakpoints (matching Ant Design)
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// Mixins for responsive design
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $value: map-get($breakpoints, $breakpoint);
    @if $value == 0 {
      @content;
    } @else {
      @media (min-width: $value) {
        @content;
      }
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin respond-below($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $value: map-get($breakpoints, $breakpoint);
    @if $value > 0 {
      @media (max-width: $value - 1px) {
        @content;
      }
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin respond-between($min, $max) {
  @if map-has-key($breakpoints, $min) and map-has-key($breakpoints, $max) {
    $min-value: map-get($breakpoints, $min);
    $max-value: map-get($breakpoints, $max);
    @media (min-width: $min-value) and (max-width: $max-value - 1px) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoints: #{$min} or #{$max}";
  }
}

// Container mixins
@mixin container($max-width: 1200px) {
  width: 100%;
  max-width: $max-width;
  margin: 0 auto;
  padding: 0 1rem;

  @include respond-below(sm) {
    padding: 0 0.5rem;
  }
}

@mixin container-fluid {
  width: 100%;
  padding: 0 1rem;

  @include respond-below(sm) {
    padding: 0 0.5rem;
  }
}

// Grid mixins
@mixin grid-container($gap: 1rem) {
  display: grid;
  gap: $gap;

  @include respond-below(sm) {
    gap: $gap * 0.5;
  }
}

@mixin grid-columns($columns) {
  @if type-of($columns) == 'map' {
    @each $breakpoint, $cols in $columns {
      @include respond-to($breakpoint) {
        grid-template-columns: repeat($cols, 1fr);
      }
    }
  } @else {
    grid-template-columns: repeat($columns, 1fr);
  }
}

// Flexbox mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

// Typography mixins
@mixin responsive-font($min-size, $max-size, $min-width: 320px, $max-width: 1200px) {
  font-size: $min-size;

  @media (min-width: $min-width) and (max-width: $max-width) {
    font-size: calc(#{$min-size} + #{strip-unit($max-size - $min-size)} * ((100vw - #{$min-width}) / #{strip-unit($max-width - $min-width)}));
  }

  @media (min-width: $max-width) {
    font-size: $max-size;
  }
}

@function strip-unit($number) {
  @if type-of($number) == 'number' and not unitless($number) {
    @return $number / ($number * 0 + 1);
  }
  @return $number;
}

// Spacing mixins
@mixin responsive-spacing($property, $mobile, $tablet: null, $desktop: null) {
  #{$property}: $mobile;

  @if $tablet {
    @include respond-to(md) {
      #{$property}: $tablet;
    }
  }

  @if $desktop {
    @include respond-to(lg) {
      #{$property}: $desktop;
    }
  }
}

// Utility classes
.container {
  @include container;
}

.container-fluid {
  @include container-fluid;
}

.container-sm {
  @include container(800px);
}

.container-lg {
  @include container(1400px);
}

// Responsive display utilities
.d-none {
  display: none !important;
}

.d-block {
  display: block !important;
}

.d-flex {
  display: flex !important;
}

.d-grid {
  display: grid !important;
}

// Responsive display for each breakpoint
@each $breakpoint in map-keys($breakpoints) {
  @include respond-to($breakpoint) {
    .d-#{$breakpoint}-none {
      display: none !important;
    }
    .d-#{$breakpoint}-block {
      display: block !important;
    }
    .d-#{$breakpoint}-flex {
      display: flex !important;
    }
    .d-#{$breakpoint}-grid {
      display: grid !important;
    }
  }
}

// Text alignment utilities
.text-left {
  text-align: left !important;
}

.text-center {
  text-align: center !important;
}

.text-right {
  text-align: right !important;
}

@each $breakpoint in map-keys($breakpoints) {
  @include respond-to($breakpoint) {
    .text-#{$breakpoint}-left {
      text-align: left !important;
    }
    .text-#{$breakpoint}-center {
      text-align: center !important;
    }
    .text-#{$breakpoint}-right {
      text-align: right !important;
    }
  }
}

// Spacing utilities
$spacers: (
  0: 0,
  1: 0.25rem,
  2: 0.5rem,
  3: 1rem,
  4: 1.5rem,
  5: 3rem
);

@each $prop, $abbrev in (margin: m, padding: p) {
  @each $size, $length in $spacers {
    .#{$abbrev}-#{$size} {
      #{$prop}: $length !important;
    }
    .#{$abbrev}t-#{$size} {
      #{$prop}-top: $length !important;
    }
    .#{$abbrev}r-#{$size} {
      #{$prop}-right: $length !important;
    }
    .#{$abbrev}b-#{$size} {
      #{$prop}-bottom: $length !important;
    }
    .#{$abbrev}l-#{$size} {
      #{$prop}-left: $length !important;
    }
    .#{$abbrev}x-#{$size} {
      #{$prop}-left: $length !important;
      #{$prop}-right: $length !important;
    }
    .#{$abbrev}y-#{$size} {
      #{$prop}-top: $length !important;
      #{$prop}-bottom: $length !important;
    }
  }
}

// Responsive spacing
@each $breakpoint in map-keys($breakpoints) {
  @include respond-to($breakpoint) {
    @each $prop, $abbrev in (margin: m, padding: p) {
      @each $size, $length in $spacers {
        .#{$abbrev}-#{$breakpoint}-#{$size} {
          #{$prop}: $length !important;
        }
        .#{$abbrev}t-#{$breakpoint}-#{$size} {
          #{$prop}-top: $length !important;
        }
        .#{$abbrev}r-#{$breakpoint}-#{$size} {
          #{$prop}-right: $length !important;
        }
        .#{$abbrev}b-#{$breakpoint}-#{$size} {
          #{$prop}-bottom: $length !important;
        }
        .#{$abbrev}l-#{$breakpoint}-#{$size} {
          #{$prop}-left: $length !important;
        }
        .#{$abbrev}x-#{$breakpoint}-#{$size} {
          #{$prop}-left: $length !important;
          #{$prop}-right: $length !important;
        }
        .#{$abbrev}y-#{$breakpoint}-#{$size} {
          #{$prop}-top: $length !important;
          #{$prop}-bottom: $length !important;
        }
      }
    }
  }
}

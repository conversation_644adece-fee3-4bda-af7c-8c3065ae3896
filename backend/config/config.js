const config = {
  production: {
    websiteName: 'Demo',
    domain: process.env.DOMAIN || 'https://demo.stealersmile.click',
    client_id: "1056580844777-gvns2idkt1kquld7aut7eks1g0kg9pr9.apps.googleusercontent.com",
    client_secret: "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",
    redirect_uri: `${process.env.DOMAIN || "https://demo.stealersmile.click"}/auth/login-google`,
    backend_base_url: 'http://localhost:3000',
    supportEmail: "<EMAIL>",
    mail: process.env.EMAIL_API_KEY || "re_NUFGvfWm_CM5KGY57X2pTT8AJ1H9DJwRt",
  },
  development: {
    websiteName: 'Demo',
    domain: process.env.DOMAIN || 'http://localhost:8080',
    client_id: "1056580844777-gvns2idkt1kquld7aut7eks1g0kg9pr9.apps.googleusercontent.com",
    client_secret: "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",
    redirect_uri: "http://localhost:8080/auth/login-google",
    backend_base_url: '',
    supportEmail: "<EMAIL>",
    mail: process.env.EMAIL_API_KEY || "re_NUFGvfWm_CM5KGY57X2pTT8AJ1H9DJwRt",
    payment: {
      vnpay: {
        vnp_TmnCode: process.env.VNPAY_TMN || 'CLKETEST',
        vnp_HashSecret: process.env.VNPAY_HASH || 'SQV53YQ7G82ASHBAQ7KO1SFZZCYSD39Y',
        vnp_Url: process.env.VNPAY_URL || 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html',
        vnp_ReturnUrl: `${process.env.DOMAIN || 'http://localhost:8080'}/payment/vnpay/return`,
        vnp_Whitelist: `${process.env.VNPAY_WHITELIST || '**************,**************,************'}`,
      },
      vnptpay: {
        vnptMerchant: process.env.VNPT_MERCHANT || 'Clickee',
        vnptMerchantId: process.env.VNPT_MERCHANT_ID || '2427',
        vnptMerchantService: process.env.VNPT_MERCHANT_SERVICE || 'Clickee',
        vnptMerchantServiceId: process.env.VNPT_MERCHANT_SERVICE_ID || '6072',
        vnptBaseUrl: process.env.VNPT_BASE_URL || 'https://sandboxpaydev.vnptmedia.vn/rest/payment/v1.0.6/',
        vnptApiKey: process.env.VNPT_API_KEY || '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        vnptSecretKey: process.env.VNPT_SECRET_KEY || 'afc2d760c47c22979fa98f90d7667db6',

        // Config for VNPT QR
        vnptQrBaseUrl: process.env.VNPT_QR_BASE_URL || 'https://sandboxpaydev.vnptmedia.vn/rest/vietqr/merchant/1.0.0/',
        vnptQrApiKey: process.env.VNPT_QR_API_KEY || '*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        vnptQrSecretKey: process.env.VNPT_QR_SECRET_KEY || '85604cb96aaf6653c5e36d42edbcc461',
        vnptQrMerchantClientId: process.env.VNPT_QR_MERCHANT_CLIENT_ID || '31618',
        vnptQrMerchantCode: process.env.VNPT_QR_MERCHANT_CODE || '21574',
        vnptQrMerchantName: process.env.VNPT_QR_MERCHANT_NAME || 'Clickee',
        vnptQrTerminalId: process.env.VNPT_QR_TERMINAL_ID || '12413',
      },
    },
  },
};

exports.getConfig = env => config[env] || config.development;

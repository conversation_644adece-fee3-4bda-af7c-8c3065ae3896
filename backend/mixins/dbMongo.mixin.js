const DbService = require('moleculer-db');
const MongooseAdapter = require('moleculer-db-adapter-mongoose');
const connectionManager = require('../utils/mongoConnectionManager');

// Cache các adapter theo URI để tái sử dụng kết nối
const adapterCache = new Map();

// Khởi tạo connection manager
connectionManager.setupConnectionListeners();

module.exports = function(mongooseModel) {
  const mongoDbUri =
    process.env.MONGO_URI || "mongodb+srv://stearlersmile:<EMAIL>/demo-shop";
    // process.env.MONGO_URI || "mongodb+srv://stealersmile:<EMAIL>/demo?tls=true&authMechanism=SCRAM-SHA-256&retrywrites=false&maxIdleTimeMS=120000";
    // process.env.MONGO_URI || 'mongodb://127.0.0.1:27017/demo';
    // process.env.MONGO_URI || 'mongodb://demo:xuanhung2801@103.152.165.178:27017/demo'

  // Tạo adapter với cache để tái sử dụng kết nối
  let adapter;
  if (adapterCache.has(mongoDbUri)) {
    adapter = adapterCache.get(mongoDbUri);
  } else {
    adapter = new MongooseAdapter(mongoDbUri, {
      // Cấu hình MongoDB để tránh memory leak
      maxPoolSize: 10, // Giới hạn số kết nối trong pool
      serverSelectionTimeoutMS: 5000, // Timeout khi chọn server
      socketTimeoutMS: 45000, // Timeout cho socket
      bufferMaxEntries: 0, // Tắt buffering
      bufferCommands: false, // Tắt command buffering
    });

    // Tăng giới hạn MaxListeners để tránh warning
    if (adapter.db && adapter.db.setMaxListeners) {
      adapter.db.setMaxListeners(50);
    }

    adapterCache.set(mongoDbUri, adapter);
  }

  return {
    mixins: [DbService],
    adapter: adapter,
    model: mongooseModel,
    actions: {
      create: {
        visibility: 'published',
      },
      update: {
        visibility: 'published',
      },
      list: {
        visibility: 'published',
      },
      get: {
        visibility: 'published',
      },
      remove: {
        visibility: 'published',
      },
      insertMany: {
        async handler(ctx) {
          return this.adapter.insertMany(ctx.params);
        },
      },
    },

    // Xử lý khi service dừng
    async stopped() {
      // Không đóng adapter ở đây vì có thể có service khác đang sử dụng
      // Adapter sẽ được đóng khi process kết thúc
      if (this.logger) {
        this.logger.debug('Service stopped, keeping MongoDB connection for other services');
      }
    },

    // Xử lý khi service khởi động
    async started() {
      // Đảm bảo kết nối được thiết lập đúng cách
      try {
        if (this.adapter && this.adapter.connect) {
          await this.adapter.connect();
        }
        if (this.logger) {
          this.logger.info('MongoDB connection established successfully');
        }
      } catch (error) {
        if (this.logger) {
          this.logger.error('Error connecting to MongoDB:', error);
        }
        throw error;
      }
    }
  };
};

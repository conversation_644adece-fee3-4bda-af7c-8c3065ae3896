const mongoose = require('mongoose');

class MongoConnectionManager {
  constructor() {
    this.connections = new Map();
    this.isShuttingDown = false;
    
    // Xử lý khi process kết thúc
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('beforeExit', () => this.gracefulShutdown('beforeExit'));
  }

  async gracefulShutdown(signal) {
    if (this.isShuttingDown) return;
    
    this.isShuttingDown = true;
    console.log(`\n${signal} received. Closing MongoDB connections gracefully...`);
    
    try {
      // <PERSON><PERSON>g tất cả kết nối mongoose
      await mongoose.disconnect();
      console.log('All MongoDB connections closed successfully');
    } catch (error) {
      console.error('Error closing MongoDB connections:', error);
    }
    
    process.exit(0);
  }

  // Thiết lập MaxListeners cho mongoose connection
  setupConnectionListeners() {
    if (mongoose.connection && mongoose.connection.setMaxListeners) {
      mongoose.connection.setMaxListeners(50);
    }
    
    // Thiết lập listeners cho các event
    mongoose.connection.on('connected', () => {
      console.log('Mongoose connected to MongoDB');
    });
    
    mongoose.connection.on('error', (err) => {
      console.error('Mongoose connection error:', err);
    });
    
    mongoose.connection.on('disconnected', () => {
      console.log('Mongoose disconnected from MongoDB');
    });
  }
}

// Tạo instance singleton
const connectionManager = new MongoConnectionManager();

module.exports = connectionManager;

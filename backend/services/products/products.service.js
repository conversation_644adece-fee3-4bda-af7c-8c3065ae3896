const DbMongoose = require('../../mixins/dbMongo.mixin');
const Model = require('./products.model');
const BaseService = require('../../mixins/baseService.mixin');
const AdminService = require('../../mixins/adminService.mixin');
const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const XLSX = require('xlsx');
const { Schema } = require("mongoose");
const { CATEGORY, FILE } = require("../../constants/dbCollections");
const { MoleculerError } = require('moleculer').Errors;

module.exports = {
  name: 'products',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon, AdminService],
  settings: {
    populates: {
      categoryIds: 'categories',
      images: 'files'
    },
    populateOptions: [],
  },

  hooks: {
    before: {
      async create(ctx) {
        ctx.params.slug = this.generateSlug(ctx.params.name);
        const data = await this.adapter.findOne({ slug: ctx.params.slug });
        if ( data ) {
          throw new MoleculerError('Danh mục với slug này đã tồn tại', 400);
        }
      },
      async update(ctx) {
        if ( ctx.params.name ) {
          ctx.params.slug = this.generateSlug(ctx.params.name);
        }
        const data = this.adapter.findOne({ slug: ctx.params.slug });
        if ( data ) {
          throw new MoleculerError('Danh mục với slug này đã tồn tại', 400);
        }
      }
    },
    after: {
      async create(ctx) {
      },
      update: async function (ctx) {

      }
    }
  },

  actions: {
    copy: {
      rest: 'POST /:id/copy',
      async handler(ctx) {
        const { id } = ctx.params;

        const product = await this.adapter.findById(id);
        if ( !product ) {
          throw new MoleculerError('Sản phẩm không tồn tại', 404);
        }

        const duplicateData = {
          name: `${product.name} (Copy)`,
          slug: `${product.slug}-copy-${Date.now()}`,
          description: product.description,
          categoryIds: product.categoryIds,
          attributes: product.attributes,
          images: product.images,
          isActive: false
        };


        const duplicatedProduct = await this.adapter.insert(duplicateData);

        this.broker.emit('product.copy', { productId: id, newProductId: duplicatedProduct._id });

        return duplicatedProduct;
      }
    },


    // Lấy chi tiết sản phẩm kèm variants
    getDetail: {
      params: {
        id: 'string'
      },
      async handler(ctx) {
        const { id } = ctx.params;

        const product = await this.adapter.findById(id, {
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });

        if ( !product ) {
          throw new MoleculerError('Sản phẩm không tồn tại', 404);
        }

        // Lấy danh sách variants
        const variants = await ctx.call('variants.find', {
          query: { productId: id },
          populate: ['images']
        });

        // Tăng view count (có thể dùng cho trending)
        await this.adapter.updateById(id, {
          $inc: { viewCount: 1 }
        });

        return {
          ...product,
          variants
        };
      }
    },

    // Tìm kiếm và lọc sản phẩm
    search: {
      rest: 'GET /search',
      params: {
        q: { type: 'string', optional: true }, // Search query
        category: { type: 'string', optional: true }, // Category ID
        minPrice: { type: 'number', optional: true },
        maxPrice: { type: 'number', optional: true },
        sortBy: { type: 'enum', values: ['name', 'price', 'createdAt', 'totalSold', 'averageRating'], optional: true, default: 'createdAt' },
        sortOrder: { type: 'enum', values: ['asc', 'desc'], optional: true, default: 'desc' },
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 },
        inStock: { type: 'boolean', optional: true },
        isFeatured: { type: 'boolean', optional: true },
        isNewProduct: { type: 'boolean', optional: true }
      },
      async handler(ctx) {
        const { q, category, minPrice, maxPrice, sortBy, sortOrder, page, limit, inStock, isFeatured, isNewProduct } = ctx.params;

        // Build query
        const query = { isDeleted: false };

        // Text search
        if (q) {
          query.$or = [
            { name: { $regex: q, $options: 'i' } },
            { description: { $regex: q, $options: 'i' } }
          ];
        }

        // Category filter
        if (category) {
          query.categoryIds = category;
        }

        // Feature flags
        if (isFeatured !== undefined) {
          query.isFeatured = isFeatured;
        }
        if (isNewProduct !== undefined) {
          query.isNewProduct = isNewProduct;
        }

        // Price range filter (sẽ join với variants)
        let priceFilter = {};
        if (minPrice !== undefined || maxPrice !== undefined) {
          if (minPrice !== undefined) priceFilter.$gte = minPrice;
          if (maxPrice !== undefined) priceFilter.$lte = maxPrice;
        }

        // Build aggregation pipeline
        const pipeline = [
          { $match: query },

          // Lookup variants để lấy giá
          {
            $lookup: {
              from: 'Variant',
              localField: '_id',
              foreignField: 'productId',
              as: 'variants'
            }
          },

          // Filter by price if specified
          ...(Object.keys(priceFilter).length > 0 ? [{
            $match: {
              'variants.price': priceFilter
            }
          }] : []),

          // Filter by stock if specified
          ...(inStock ? [{
            $match: {
              'variants.totalStock': { $gt: 0 }
            }
          }] : []),

          // Add computed fields
          {
            $addFields: {
              minPrice: { $min: '$variants.price' },
              maxPrice: { $max: '$variants.price' },
              totalStock: { $sum: '$variants.totalStock' }
            }
          },

          // Lookup categories
          {
            $lookup: {
              from: 'Category',
              localField: 'categoryIds',
              foreignField: '_id',
              as: 'categories'
            }
          },

          // Lookup images
          {
            $lookup: {
              from: 'File',
              localField: 'images',
              foreignField: '_id',
              as: 'imageFiles'
            }
          }
        ];

        // Sort
        const sortObj = {};
        sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;
        pipeline.push({ $sort: sortObj });

        // Pagination
        const skip = (page - 1) * limit;
        pipeline.push({ $skip: skip });
        pipeline.push({ $limit: limit });

        // Execute aggregation
        const products = await this.adapter.model.aggregate(pipeline);

        // Get total count for pagination
        const countPipeline = pipeline.slice(0, -2); // Remove skip and limit
        countPipeline.push({ $count: 'total' });
        const countResult = await this.adapter.model.aggregate(countPipeline);
        const total = countResult[0]?.total || 0;

        return {
          docs: products,
          total,
          page,
          limit,
          pages: Math.ceil(total / limit)
        };
      }
    },

    // Lấy sản phẩm bestseller
    getBestsellers: {
      rest: 'GET /bestsellers',
      params: {
        limit: { type: 'number', optional: true, default: 10 },
        category: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { limit, category } = ctx.params;

        const query = {
          isDeleted: false,
          totalSold: { $gt: 0 }
        };

        if (category) {
          query.categoryIds = category;
        }

        const products = await this.adapter.find({
          query,
          sort: { totalSold: -1 },
          limit,
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });

        // Lấy giá min/max cho mỗi sản phẩm
        for (const product of products) {
          const variants = await ctx.call('variants.find', {
            query: { productId: product._id },
            fields: ['price', 'totalStock']
          });

          if (variants.length > 0) {
            product.minPrice = Math.min(...variants.map(v => v.price));
            product.maxPrice = Math.max(...variants.map(v => v.price));
            product.totalStock = variants.reduce((sum, v) => sum + v.totalStock, 0);
          }
        }

        return products;
      }
    },

    // Lấy sản phẩm nổi bật
    getFeatured: {
      rest: 'GET /featured',
      params: {
        limit: { type: 'number', optional: true, default: 10 }
      },
      async handler(ctx) {
        const { limit } = ctx.params;

        const products = await this.adapter.find({
          query: {
            isDeleted: false,
            isFeatured: true
          },
          sort: { createdAt: -1 },
          limit,
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });

        // Lấy giá min/max cho mỗi sản phẩm
        for (const product of products) {
          const variants = await ctx.call('variants.find', {
            query: { productId: product._id },
            fields: ['price', 'totalStock']
          });

          if (variants.length > 0) {
            product.minPrice = Math.min(...variants.map(v => v.price));
            product.maxPrice = Math.max(...variants.map(v => v.price));
            product.totalStock = variants.reduce((sum, v) => sum + v.totalStock, 0);
          }
        }

        return products;
      }
    },

    // Lấy sản phẩm mới
    getNewProducts: {
      rest: 'GET /new',
      params: {
        limit: { type: 'number', optional: true, default: 10 }
      },
      async handler(ctx) {
        const { limit } = ctx.params;

        const products = await this.adapter.find({
          query: {
            isDeleted: false,
            isNewProduct: true
          },
          sort: { createdAt: -1 },
          limit,
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });

        // Lấy giá min/max cho mỗi sản phẩm
        for (const product of products) {
          const variants = await ctx.call('variants.find', {
            query: { productId: product._id },
            fields: ['price', 'totalStock']
          });

          if (variants.length > 0) {
            product.minPrice = Math.min(...variants.map(v => v.price));
            product.maxPrice = Math.max(...variants.map(v => v.price));
            product.totalStock = variants.reduce((sum, v) => sum + v.totalStock, 0);
          }
        }

        return products;
      }
    },

    // Lấy sản phẩm liên quan
    getRelatedProducts: {
      rest: 'GET /:id/related',
      params: {
        id: 'string',
        limit: { type: 'number', optional: true, default: 8 }
      },
      async handler(ctx) {
        const { id, limit } = ctx.params;

        // Lấy thông tin sản phẩm hiện tại
        const currentProduct = await this.adapter.findById(id);
        if (!currentProduct) {
          throw new MoleculerError('Sản phẩm không tồn tại', 404);
        }

        // Tìm sản phẩm cùng category
        const relatedProducts = await this.adapter.find({
          query: {
            _id: { $ne: id },
            categoryIds: { $in: currentProduct.categoryIds },
            isDeleted: false
          },
          sort: { totalSold: -1, createdAt: -1 },
          limit,
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });

        // Nếu không đủ sản phẩm cùng category, lấy thêm sản phẩm khác
        if (relatedProducts.length < limit) {
          const additionalProducts = await this.adapter.find({
            query: {
              _id: {
                $ne: id,
                $nin: relatedProducts.map(p => p._id)
              },
              isDeleted: false
            },
            sort: { totalSold: -1, createdAt: -1 },
            limit: limit - relatedProducts.length,
            populate: [
              { path: 'categoryIds', select: 'name slug' },
              { path: 'images' }
            ]
          });

          relatedProducts.push(...additionalProducts);
        }

        // Lấy giá min/max cho mỗi sản phẩm
        for (const product of relatedProducts) {
          const variants = await ctx.call('variants.find', {
            query: { productId: product._id },
            fields: ['price', 'totalStock']
          });

          if (variants.length > 0) {
            product.minPrice = Math.min(...variants.map(v => v.price));
            product.maxPrice = Math.max(...variants.map(v => v.price));
            product.totalStock = variants.reduce((sum, v) => sum + v.totalStock, 0);
          }
        }

        return relatedProducts;
      }
    },

    // Lấy sản phẩm theo category
    getByCategory: {
      rest: 'GET /category/:categoryId',
      params: {
        categoryId: 'string',
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 },
        sortBy: { type: 'enum', values: ['name', 'price', 'createdAt', 'totalSold'], optional: true, default: 'createdAt' },
        sortOrder: { type: 'enum', values: ['asc', 'desc'], optional: true, default: 'desc' }
      },
      async handler(ctx) {
        const { categoryId, page, limit, sortBy, sortOrder } = ctx.params;

        const sortObj = {};
        sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;

        const options = {
          page,
          limit,
          sort: sortObj,
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        };

        const result = await this.adapter.paginate({
          query: {
            categoryIds: categoryId,
            isDeleted: false
          },
          options
        });

        // Lấy giá min/max cho mỗi sản phẩm
        for (const product of result.docs) {
          const variants = await ctx.call('variants.find', {
            query: { productId: product._id },
            fields: ['price', 'totalStock']
          });

          if (variants.length > 0) {
            product.minPrice = Math.min(...variants.map(v => v.price));
            product.maxPrice = Math.max(...variants.map(v => v.price));
            product.totalStock = variants.reduce((sum, v) => sum + v.totalStock, 0);
          }
        }

        return result;
      }
    },

    // Cập nhật rating sản phẩm (được gọi từ reviews service)
    updateRating: {
      params: {
        productId: 'string',
        averageRating: 'number',
        totalReviews: 'number'
      },
      async handler(ctx) {
        const { productId, averageRating, totalReviews } = ctx.params;

        return await this.adapter.updateById(productId, {
          averageRating,
          totalReviews
        });
      }
    },

    // Cập nhật số lượng đã bán (được gọi từ orders service)
    updateSoldCount: {
      params: {
        productId: 'string',
        quantity: 'number'
      },
      async handler(ctx) {
        const { productId, quantity } = ctx.params;

        return await this.adapter.updateById(productId, {
          $inc: { totalSold: quantity }
        });
      }
    },

    // Lấy sản phẩm theo category
    // getByCategory: {
    //   params: {
    //     categoryId: 'string',
    //     page: { type: 'number', optional: true, default: 1 },
    //     limit: { type: 'number', optional: true, default: 20 }
    //   },
    //   async handler(ctx) {
    //     return await ctx.call('products.list', {
    //       ...ctx.params,
    //       categoryId: ctx.params.categoryId,
    //       isActive: true
    //     });
    //   }
    // },

    // Tìm kiếm sản phẩm
    // search: {
    //   params: {
    //     keyword: 'string',
    //     page: { type: 'number', optional: true, default: 1 },
    //     limit: { type: 'number', optional: true, default: 20 }
    //   },
    //   async handler(ctx) {
    //     return await ctx.call('products.list', {
    //       ...ctx.params,
    //       search: ctx.params.keyword,
    //       isActive: true
    //     });
    //   }
    // },

    // Lấy sản phẩm liên quan
    getRelated: {
      params: {
        productId: 'string',
        limit: { type: 'number', optional: true, default: 4 }
      },
      async handler(ctx) {
        const { productId, limit } = ctx.params;

        const product = await this.adapter.findById(productId);
        if ( !product ) {
          throw new MoleculerError('Sản phẩm không tồn tại', 404);
        }

        // Tìm sản phẩm cùng category
        const query = {
          categoryIds: { $in: product.categoryIds },
          _id: { $ne: productId },
          isActive: true
        };

        return await this.adapter.find({
          query,
          limit,
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });
      }
    },

    // Lấy sản phẩm bán chạy
    getBestSelling: {
      params: {
        limit: { type: 'number', optional: true, default: 10 },
        categoryId: { type: 'string', optional: true },
        timeRange: { type: 'number', optional: true, default: 30 } // số ngày
      },
      async handler(ctx) {
        const { limit, categoryId, timeRange } = ctx.params;

        // Tính toán từ orders (cần tích hợp với order service)
        // Tạm thời return mock data hoặc dựa vào totalSold nếu có field này
        let query = { isActive: true };

        if ( categoryId ) {
          query.categoryIds = categoryId;
        }

        return await this.adapter.find({
          query,
          limit,
          sort: { totalSold: -1, createdAt: -1 }, // Giả sử có field totalSold
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });
      }
    },

    // Lấy sản phẩm mới
    getLatest: {
      params: {
        limit: { type: 'number', optional: true, default: 10 },
        categoryId: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { limit, categoryId } = ctx.params;

        let query = { isActive: true };

        if ( categoryId ) {
          query.categoryIds = categoryId;
        }

        return await this.adapter.find({
          query,
          limit,
          sort: { createdAt: -1 },
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });
      }
    },

    // Thống kê sản phẩm
    getStats: {
      async handler(ctx) {
        const totalProducts = await this.adapter.count();
        const activeProducts = await this.adapter.count({ isActive: true });
        const inactiveProducts = await this.adapter.count({ isActive: false });

        // Thống kê theo category
        const productsByCategory = await this.adapter.model.aggregate([
          { $match: { isActive: true } },
          { $unwind: '$categoryIds' },
          {
            $lookup: {
              from: 'categories',
              localField: 'categoryIds',
              foreignField: '_id',
              as: 'category'
            }
          },
          { $unwind: '$category' },
          {
            $group: {
              _id: '$category._id',
              categoryName: { $first: '$category.name' },
              count: { $sum: 1 }
            }
          },
          { $sort: { count: -1 } },
          { $limit: 10 }
        ]);

        // Sản phẩm được tạo trong 7 ngày qua
        const last7Days = new Date();
        last7Days.setDate(last7Days.getDate() - 7);
        const newProductsLast7Days = await this.adapter.count({
          createdAt: { $gte: last7Days }
        });

        return {
          totalProducts,
          activeProducts,
          inactiveProducts,
          newProductsLast7Days,
          productsByCategory
        };
      }
    },

    // Filter products nâng cao
    advancedFilter: {
      params: {
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 },
        categoryIds: { type: 'array', items: 'string', optional: true },
        priceMin: { type: 'number', optional: true },
        priceMax: { type: 'number', optional: true },
        attributes: { type: 'object', optional: true }, // { "Màu sắc": ["Đỏ", "Xanh"], "Size": ["M", "L"] }
        hasStock: { type: 'boolean', optional: true },
        sortBy: { type: 'string', optional: true, default: 'createdAt' },
        sortOrder: { type: 'string', optional: true, default: 'desc' }
      },
      async handler(ctx) {
        const { page, limit, categoryIds, priceMin, priceMax, attributes, hasStock, sortBy, sortOrder } = ctx.params;

        let query = { isActive: true };

        // Filter theo categories
        if ( categoryIds && categoryIds.length > 0 ) {
          query.categoryIds = { $in: categoryIds };
        }

        // Filter theo attributes
        if ( attributes && Object.keys(attributes).length > 0 ) {
          const attributeConditions = [];

          Object.entries(attributes).forEach(([attrName, attrValues]) => {
            if ( Array.isArray(attrValues) && attrValues.length > 0 ) {
              attributeConditions.push({
                'attributes': {
                  $elemMatch: {
                    name: attrName,
                    values: { $in: attrValues }
                  }
                }
              });
            }
          });

          if ( attributeConditions.length > 0 ) {
            query.$and = attributeConditions;
          }
        }

        const options = {
          page,
          limit,
          sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 },
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        };

        let products = await this.adapter.paginate({ query, options });

        // Filter theo giá (dựa vào variants)
        if ( priceMin !== undefined || priceMax !== undefined || hasStock !== undefined ) {
          const productIds = products.docs.map(p => p._id);

          let variantQuery = { productId: { $in: productIds } };

          if ( priceMin !== undefined || priceMax !== undefined ) {
            variantQuery.price = {};
            if ( priceMin !== undefined ) variantQuery.price.$gte = priceMin;
            if ( priceMax !== undefined ) variantQuery.price.$lte = priceMax;
          }

          const validVariants = await ctx.call('variants.find', { query: variantQuery });
          const validProductIds = [...new Set(validVariants.map(v => v.productId))];

          // Filter hasStock
          if ( hasStock !== undefined ) {
            const stockPromises = validVariants.map(variant =>
              ctx.call('inventories.getByVariant', { variantId: variant._id })
                .then(inv => inv && inv.availableStock > 0 ? variant.productId : null)
                .catch(() => null)
            );

            const stockResults = await Promise.all(stockPromises);
            const inStockProductIds = [...new Set(stockResults.filter(id => id !== null))];

            if ( hasStock ) {
              products.docs = products.docs.filter(p => inStockProductIds.includes(p._id.toString()));
            } else {
              products.docs = products.docs.filter(p => !inStockProductIds.includes(p._id.toString()));
            }
          } else {
            products.docs = products.docs.filter(p => validProductIds.includes(p._id.toString()));
          }

          // Cập nhật lại totalDocs
          products.totalDocs = products.docs.length;
          products.totalPages = Math.ceil(products.totalDocs / limit);
        }

        return products;
      }
    },

    // So sánh nhiều sản phẩm
    compare: {
      cache: {
        keys: ['productIds'],
        ttl: 300 // Cache 5 phút
      },
      params: {
        productIds: {
          type: 'array',
          items: 'string',
          min: 2,
          max: 4 // Giới hạn so sánh tối đa 4 sản phẩm
        }
      },
      async handler(ctx) {
        const { productIds } = ctx.params;

        // Lấy thông tin chi tiết của tất cả sản phẩm
        const products = await Promise.all(
          productIds.map(async (productId) => {
            try {
              return await ctx.call('productPublic.getDetail', { id: productId });
            } catch ( error ) {
              this.logger.warn(`Failed to get product ${productId}:`, error.message);
              return null;
            }
          })
        );

        const validProducts = products.filter(p => p !== null);

        if ( validProducts.length < 2 ) {
          throw new MoleculerError('Cần ít nhất 2 sản phẩm hợp lệ để so sánh', 400);
        }

        // Tạo comparison matrix
        const comparison = {
          products: validProducts.map(product => ({
            id: product._id,
            name: product.name,
            slug: product.slug,
            images: product.images,
            description: product.description,
            categories: product.categoryIds,
            averageRating: product.averageRating,
            totalReviews: product.totalReviews,
            priceRange: this.calculatePriceRange(product.variants),
            availability: this.calculateAvailability(product.variants),
            attributes: this.normalizeAttributes(product.attributes),
            variants: product.variants,
            specifications: this.extractSpecifications(product)
          })),
          comparisonMatrix: this.buildComparisonMatrix(validProducts),
          summary: this.generateComparisonSummary(validProducts)
        };

        return comparison;
      }
    },

    // Lấy sản phẩm tương tự để so sánh
    getSimilarForComparison: {
      cache: {
        keys: ['productId', 'limit'],
        ttl: 600
      },
      params: {
        productId: 'string',
        limit: { type: 'number', optional: true, default: 6, max: 10 }
      },
      async handler(ctx) {
        const { productId, limit } = ctx.params;

        // Lấy thông tin sản phẩm gốc
        const baseProduct = await ctx.call('productPublic.getDetail', { id: productId });
        if ( !baseProduct ) {
          throw new MoleculerError('Sản phẩm không tồn tại', 404);
        }

        // Tìm sản phẩm cùng category với price range tương tự
        const basePrice = this.calculatePriceRange(baseProduct.variants);
        const priceMargin = basePrice.average * 0.3; // 30% margin

        const similarProducts = await ctx.call('products.find', {
          query: {
            categoryIds: { $in: baseProduct.categoryIds.map(c => c._id) },
            _id: { $ne: baseProduct._id },
            isActive: true
          },
          limit: limit * 2, // Lấy nhiều hơn để filter
          sort: { averageRating: -1, totalSold: -1 },
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images' }
          ]
        });

        // Lọc theo price range và thêm thông tin variants
        const filteredProducts = [];

        for ( const product of similarProducts ) {
          if ( filteredProducts.length >= limit ) break;

          try {
            const variants = await ctx.call('variants.find', {
              query: { productId: product._id }
            });

            const productPriceRange = this.calculatePriceRange(variants);

            // Chỉ lấy sản phẩm có giá trong khoảng tương tự
            if ( Math.abs(productPriceRange.average - basePrice.average) <= priceMargin ) {
              filteredProducts.push({
                ...product.toObject(),
                priceRange: productPriceRange,
                variantCount: variants.length
              });
            }
          } catch ( error ) {
            // Skip sản phẩm lỗi

          }
        }

        return {
          baseProduct: {
            id: baseProduct._id,
            name: baseProduct.name,
            slug: baseProduct.slug,
            images: baseProduct.images,
            priceRange: basePrice
          },
          similarProducts: filteredProducts
        };
      }
    },

    // So sánh chi tiết 2 sản phẩm cụ thể
    compareDetailed: {
      cache: {
        keys: ['productId1', 'productId2'],
        ttl: 300
      },
      params: {
        productId1: 'string',
        productId2: 'string'
      },
      async handler(ctx) {
        const { productId1, productId2 } = ctx.params;

        const [product1, product2] = await Promise.all([
          ctx.call('productPublic.getDetail', { id: productId1 }),
          ctx.call('productPublic.getDetail', { id: productId2 })
        ]);

        if ( !product1 || !product2 ) {
          throw new MoleculerError('Một hoặc cả hai sản phẩm không tồn tại', 404);
        }

        return {
          product1: this.formatProductForComparison(product1),
          product2: this.formatProductForComparison(product2),
          differences: this.findDifferences(product1, product2),
          similarities: this.findSimilarities(product1, product2),
          recommendation: this.generateRecommendation(product1, product2)
        };
      }
    },

    // Tạo comparison table cho multiple products
    getComparisonTable: {
      cache: {
        keys: ['productIds', 'fields'],
        ttl: 300
      },
      params: {
        productIds: { type: 'array', items: 'string', min: 2, max: 5 },
        fields: {
          type: 'array',
          items: 'string',
          optional: true,
          default: ['name', 'price', 'rating', 'availability', 'attributes']
        }
      },
      async handler(ctx) {
        const { productIds, fields } = ctx.params;

        const products = await Promise.all(
          productIds.map(id => ctx.call('productPublic.getDetail', { id }))
        );

        const validProducts = products.filter(p => p !== null);

        if ( validProducts.length < 2 ) {
          throw new MoleculerError('Cần ít nhất 2 sản phẩm hợp lệ', 400);
        }

        const table = {
          headers: ['Thuộc tính', ...validProducts.map(p => p.name)],
          rows: []
        };

        // Thêm các rows theo fields yêu cầu
        fields.forEach(field => {
          const row = this.buildTableRow(field, validProducts);
          if ( row ) {
            table.rows.push(row);
          }
        });

        return table;
      }
    },

    // Lưu comparison để share
    saveComparison: {
      params: {
        productIds: { type: 'array', items: 'string', min: 2, max: 4 },
        title: { type: 'string', optional: true },
        description: { type: 'string', optional: true },
        userId: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { productIds, title, description, userId } = ctx.params;

        // Tạo unique ID cho comparison
        const comparisonId = this.generateComparisonId(productIds);

        // Lưu vào cache với TTL dài
        const comparisonData = await ctx.call('productComparison.compare', { productIds });

        const savedComparison = {
          id: comparisonId,
          title: title || `So sánh ${comparisonData.products.length} sản phẩm`,
          description,
          productIds,
          data: comparisonData,
          userId,
          createdAt: new Date(),
          shareUrl: `/compare/${comparisonId}`
        };

        // Lưu vào cache với TTL 30 ngày
        await this.broker.cacher?.set(
          `comparison:${comparisonId}`,
          savedComparison,
          30 * 24 * 60 * 60 // 30 days
        );

        return {
          id: comparisonId,
          shareUrl: savedComparison.shareUrl,
          title: savedComparison.title
        };
      }
    },

    // Lấy saved comparison
    getSavedComparison: {
      params: {
        comparisonId: 'string'
      },
      async handler(ctx) {
        const { comparisonId } = ctx.params;

        const comparison = await this.broker.cacher?.get(`comparison:${comparisonId}`);

        if ( !comparison ) {
          throw new MoleculerError('Comparison không tồn tại hoặc đã hết hạn', 404);
        }

        return comparison;
      }
    },

    export: {
      params: {
        format: { type: 'string', optional: true, default: 'excel' }, // excel, csv
        categoryIds: { type: 'array', items: 'string', optional: true },
        isActive: { type: 'boolean', optional: true },
        includeVariants: { type: 'boolean', optional: true, default: false },
        includeInventory: { type: 'boolean', optional: true, default: false },
        dateFrom: { type: 'date', optional: true },
        dateTo: { type: 'date', optional: true }
      },
      async handler(ctx) {
        const { format, categoryIds, isActive, includeVariants, includeInventory, dateFrom, dateTo } = ctx.params;

        // Build query
        let query = {};

        if ( categoryIds && categoryIds.length > 0 ) {
          query.categoryIds = { $in: categoryIds };
        }

        if ( typeof isActive !== 'undefined' ) {
          query.isActive = isActive;
        }

        if ( dateFrom || dateTo ) {
          query.createdAt = {};
          if ( dateFrom ) query.createdAt.$gte = dateFrom;
          if ( dateTo ) query.createdAt.$lte = dateTo;
        }

        // Get products
        const products = await ctx.call('products.find', {
          query,
          populate: [
            { path: 'categoryIds', select: 'name slug' },
            { path: 'images', select: 'url filename' }
          ],
          sort: { createdAt: -1 }
        });

        let exportData = [];

        for ( const product of products ) {
          let baseData = {
            'ID': product._id,
            'Tên sản phẩm': product.name,
            'Slug': product.slug,
            'Mô tả': product.description || '',
            'Danh mục': product.categoryIds?.map(c => c.name).join('; ') || '',
            'Trạng thái': product.isActive ? 'Hoạt động' : 'Không hoạt động',
            'Nổi bật': product.isFeatured ? 'Có' : 'Không',
            'Sản phẩm mới': product.isNewProduct ? 'Có' : 'Không',
            'Rating TB': product.averageRating || 0,
            'Số reviews': product.totalReviews || 0,
            'Đã bán': product.totalSold || 0,
            'Meta Title': product.metaTitle || '',
            'Meta Description': product.metaDescription || '',
            'Meta Keywords': product.metaKeywords || '',
            'Hình ảnh': product.images?.url || '',
            'Ngày tạo': product.createdAt,
            'Ngày cập nhật': product.updatedAt
          };

          // Add attributes as separate columns
          if ( product.attributes && product.attributes.length > 0 ) {
            product.attributes.forEach((attr, index) => {
              baseData[`Thuộc tính ${index + 1} - Tên`] = attr.name;
              baseData[`Thuộc tính ${index + 1} - Giá trị`] = attr.values.join(', ');
            });
          }

          if ( includeVariants ) {
            // Get variants for this product
            const variants = await ctx.call('variants.find', {
              query: { productId: product._id },
              populate: ['images']
            });

            if ( variants.length > 0 ) {
              for ( const variant of variants ) {
                let variantData = {
                  ...baseData,
                  'Variant ID': variant._id,
                  'Variant SKU': variant.sku,
                  'Variant Name': variant.name,
                  'Giá': variant.price,
                  'Giá so sánh': variant.compareAtPrice || '',
                  'Giá vốn': variant.cost || '',
                  'Trọng lượng': variant.weight || '',
                  'Kích thước': variant.dimensions ?
                    `${variant.dimensions.length}x${variant.dimensions.width}x${variant.dimensions.height}` : '',
                  'Variant Images': variant.images?.map(img => img.url).join('; ') || ''
                };

                // Add variant attributes
                if ( variant.attributes && variant.attributes.length > 0 ) {
                  variant.attributes.forEach((attr, index) => {
                    variantData[`V.Attr ${index + 1} - ${attr.name}`] = attr.value;
                  });
                }

                if ( includeInventory ) {
                  try {
                    const inventory = await ctx.call('inventories.getByVariant', {
                      variantId: variant._id
                    });

                    variantData['Tồn kho tổng'] = inventory?.totalStock || 0;
                    variantData['Tồn kho khả dụng'] = inventory?.availableStock || 0;
                    variantData['Tồn kho đặt trước'] = inventory?.reservedStock || 0;
                    variantData['Mức cảnh báo'] = inventory?.reorderLevel || 0;
                    variantData['Vị trí kho'] = inventory?.location ?
                      `${inventory.location.aisle}-${inventory.location.shelf}-${inventory.location.bin}` : '';
                    variantData['Kho'] = inventory?.warehouseId?.name || '';
                  } catch ( error ) {
                    // Skip inventory if not found
                  }
                }

                exportData.push(variantData);
              }
            } else {
              exportData.push(baseData);
            }
          } else {
            exportData.push(baseData);
          }
        }

        // Generate file
        let fileContent, fileName, mimeType;

        fileContent = this.convertToExcel(exportData);
        fileName = `products_export_${Date.now()}.xlsx`;
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

        return {
          fileName,
          mimeType,
          fileContent: fileContent.toString('base64'),
          recordCount: exportData.length,
          productCount: products.length
        };
      }
    },

    // Import sản phẩm từ file
    import: {
      params: {
        fileContent: 'string', // Base64 encoded
        fileName: 'string',
        options: {
          type: 'object',
          optional: true,
          default: {},
          props: {
            updateExisting: { type: 'boolean', default: false },
            skipErrors: { type: 'boolean', default: true },
            dryRun: { type: 'boolean', default: false },
            createCategories: { type: 'boolean', default: false }
          }
        }
      },
      async handler(ctx) {
        const { fileContent, fileName, options } = ctx.params;

        try {
          // Decode file content
          const buffer = Buffer.from(fileContent, 'base64');

          // Parse file based on extension
          let data;
          if ( fileName.endsWith('.xlsx') || fileName.endsWith('.xls') ) {
            data = this.parseExcel(buffer);
          } else {
            throw new MoleculerError('Định dạng file không được hỗ trợ. Chỉ chấp nhận CSV hoặc Excel', 400);
          }

          if ( !data || data.length === 0 ) {
            throw new MoleculerError('File không có dữ liệu hoặc định dạng không đúng', 400);
          }

          // Validate data structure
          const validationResults = this.validateImportData(data);

          if ( validationResults.criticalErrors.length > 0 && !options.skipErrors ) {
            return {
              success: false,
              errors: validationResults.criticalErrors,
              warnings: validationResults.warnings,
              validRecords: validationResults.validRecords
            };
          }

          if ( options.dryRun ) {
            return {
              success: true,
              dryRun: true,
              totalRecords: data.length,
              validRecords: validationResults.validRecords,
              errors: validationResults.errors,
              warnings: validationResults.warnings,
              preview: data.slice(0, 5) // Show first 5 records
            };
          }

          // Process import
          const importResults = await this.processImport(ctx, validationResults.validData, options);

          return {
            success: true,
            ...importResults,
            errors: validationResults.errors,
            warnings: validationResults.warnings
          };

        } catch ( error ) {
          this.logger.error('Import failed:', error);
          throw new MoleculerError(`Import thất bại: ${error.message}`, 500);
        }
      }
    },

    // Lấy template để import
    getImportTemplate: {
      params: {
        format: { type: 'string', optional: true, default: 'excel' }, // excel, csv
        includeVariants: { type: 'boolean', optional: true, default: false },
        includeSampleData: { type: 'boolean', optional: true, default: false }
      },
      async handler(ctx) {
        const { format, includeVariants, includeSampleData } = ctx.params;

        let columns = [
          'Tên sản phẩm*',
          'Slug*',
          'Mô tả',
          'Danh mục*',
          'Trạng thái (1=Hoạt động, 0=Không hoạt động)',
          'Nổi bật (1=Có, 0=Không)',
          'Sản phẩm mới (1=Có, 0=Không)',
          'Meta Title',
          'Meta Description',
          'Meta Keywords',
          'Thuộc tính 1 - Tên',
          'Thuộc tính 1 - Giá trị',
          'Thuộc tính 2 - Tên',
          'Thuộc tính 2 - Giá trị',
          'URL Hình ảnh'
        ];

        if ( includeVariants ) {
          columns = columns.concat([
            'Variant SKU*',
            'Variant Name*',
            'Giá*',
            'Giá so sánh',
            'Giá vốn',
            'Tồn kho',
            'Trọng lượng (gram)',
            'Chiều dài (cm)',
            'Chiều rộng (cm)',
            'Chiều cao (cm)',
            'V.Attr 1 - Tên',
            'V.Attr 1 - Giá trị',
            'V.Attr 2 - Tên',
            'V.Attr 2 - Giá trị',
            'URL Hình ảnh Variant'
          ]);
        }

        let data = [];

        if ( includeSampleData ) {
          let sampleRow = {
            'Tên sản phẩm*': 'Áo thun nam cotton',
            'Slug*': 'ao-thun-nam-cotton',
            'Mô tả': 'Áo thun nam chất liệu cotton cao cấp, thoáng mát',
            'Danh mục*': 'Thời trang nam',
            'Trạng thái (1=Hoạt động, 0=Không hoạt động)': 1,
            'Nổi bật (1=Có, 0=Không)': 0,
            'Sản phẩm mới (1=Có, 0=Không)': 1,
            'Meta Title': 'Áo thun nam cotton cao cấp',
            'Meta Description': 'Áo thun nam chất cotton, thoáng mát, giá tốt',
            'Meta Keywords': 'áo thun, nam, cotton',
            'Thuộc tính 1 - Tên': 'Chất liệu',
            'Thuộc tính 1 - Giá trị': 'Cotton',
            'Thuộc tính 2 - Tên': 'Xuất xứ',
            'Thuộc tính 2 - Giá trị': 'Việt Nam',
            'URL Hình ảnh': 'https://example.com/image.jpg'
          };

          if ( includeVariants ) {
            sampleRow = {
              ...sampleRow,
              'Variant SKU*': 'ATNCOT-M-RED',
              'Variant Name*': 'Áo thun nam cotton - Size M - Đỏ',
              'Giá*': 150000,
              'Giá so sánh': 200000,
              'Giá vốn': 80000,
              'Tồn kho': 100,
              'Trọng lượng (gram)': 200,
              'Chiều dài (cm)': 70,
              'Chiều rộng (cm)': 50,
              'Chiều cao (cm)': 2,
              'V.Attr 1 - Tên': 'Size',
              'V.Attr 1 - Giá trị': 'M',
              'V.Attr 2 - Tên': 'Màu sắc',
              'V.Attr 2 - Giá trị': 'Đỏ',
              'URL Hình ảnh Variant': 'https://example.com/variant-image.jpg'
            };
          }

          data.push(sampleRow);
        }

        // Generate file
        let fileContent, fileName, mimeType;

        fileContent = this.convertToExcel([...data], columns);
        fileName = `import_template_${Date.now()}.xlsx`;
        mimeType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

        return {
          fileName,
          mimeType,
          fileContent: fileContent.toString('base64'),
          instructions: this.getImportInstructions()
        };
      }
    },

    // Lấy lịch sử import
    getImportHistory: {
      params: {
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 }
      },
      async handler(ctx) {
        // Trong thực tế sẽ lưu vào database
        // Tạm thời return mock data
        return {
          docs: [],
          totalDocs: 0,
          page: ctx.params.page,
          limit: ctx.params.limit,
          totalPages: 0
        };
      }
    }
  },
  methods: {
    calculatePriceRange(variants) {
      if ( !variants || variants.length === 0 ) {
        return { min: 0, max: 0, average: 0 };
      }

      const prices = variants.map(v => v.price).filter(p => p > 0);
      if ( prices.length === 0 ) {
        return { min: 0, max: 0, average: 0 };
      }

      const min = Math.min(...prices);
      const max = Math.max(...prices);
      const average = prices.reduce((sum, price) => sum + price, 0) / prices.length;

      return { min, max, average };
    },
    calculateAvailability(variants) {
      if ( !variants || variants.length === 0 ) {
        return { inStock: false, totalStock: 0, variants: 0 };
      }

      const totalStock = variants.reduce((sum, v) => sum + (v.stock?.available || 0), 0);
      const inStockVariants = variants.filter(v => v.stock?.inStock).length;

      return {
        inStock: totalStock > 0,
        totalStock,
        variants: inStockVariants,
        outOfStockVariants: variants.length - inStockVariants
      };
    },
    normalizeAttributes(attributes) {
      if ( !attributes || attributes.length === 0 ) return {};

      const normalized = {};
      attributes.forEach(attr => {
        normalized[attr.name] = attr.values;
      });

      return normalized;
    },
    extractSpecifications(product) {
      const specs = {};

      // Basic specs
      specs['Danh mục'] = product.categoryIds?.map(c => c.name).join(', ') || 'N/A';
      specs['Đánh giá'] = `${product.averageRating}/5 (${product.totalReviews} reviews)`;
      specs['Số variants'] = product.variants?.length || 0;

      // Attributes as specs
      if ( product.attributes ) {
        product.attributes.forEach(attr => {
          specs[attr.name] = attr.values.join(', ');
        });
      }

      return specs;
    },
    buildComparisonMatrix(products) {
      const matrix = {
        price: this.comparePrice(products),
        rating: this.compareRating(products),
        availability: this.compareAvailability(products),
        attributes: this.compareAttributes(products),
        categories: this.compareCategories(products)
      };

      return matrix;
    },
    comparePrice(products) {
      const priceData = products.map(p => ({
        productId: p._id,
        priceRange: this.calculatePriceRange(p.variants)
      }));

      const allPrices = priceData.map(p => p.priceRange.average).filter(p => p > 0);
      const minPrice = Math.min(...allPrices);
      const maxPrice = Math.max(...allPrices);

      return {
        data: priceData,
        cheapest: priceData.find(p => p.priceRange.average === minPrice)?.productId,
        mostExpensive: priceData.find(p => p.priceRange.average === maxPrice)?.productId,
        priceSpread: maxPrice - minPrice
      };
    },
    compareRating(products) {
      const ratingData = products.map(p => ({
        productId: p._id,
        rating: p.averageRating || 0,
        reviewCount: p.totalReviews || 0
      }));

      const highestRated = ratingData.reduce((max, current) =>
        current.rating > max.rating ? current : max
      );

      return {
        data: ratingData,
        highestRated: highestRated.productId,
        mostReviewed: ratingData.reduce((max, current) =>
          current.reviewCount > max.reviewCount ? current : max
        ).productId
      };
    },
    compareAvailability(products) {
      const availabilityData = products.map(p => ({
        productId: p._id,
        availability: this.calculateAvailability(p.variants)
      }));

      return {
        data: availabilityData,
        mostStock: availabilityData.reduce((max, current) =>
          current.availability.totalStock > max.availability.totalStock ? current : max
        ).productId
      };
    },
    compareAttributes(products) {
      const allAttributes = new Set();

      // Collect all unique attributes
      products.forEach(product => {
        if ( product.attributes ) {
          product.attributes.forEach(attr => {
            allAttributes.add(attr.name);
          });
        }
      });

      const comparison = {};

      allAttributes.forEach(attrName => {
        comparison[attrName] = products.map(product => {
          const attr = product.attributes?.find(a => a.name === attrName);
          return {
            productId: product._id,
            values: attr ? attr.values : []
          };
        });
      });

      return comparison;
    },
    compareCategories(products) {
      const categoryData = products.map(p => ({
        productId: p._id,
        categories: p.categoryIds?.map(c => c.name) || []
      }));

      // Find common categories
      const allCategories = categoryData.flatMap(p => p.categories);
      const uniqueCategories = [...new Set(allCategories)];

      const commonCategories = uniqueCategories.filter(category => {
        return categoryData.every(p => p.categories.includes(category));
      });

      return {
        data: categoryData,
        commonCategories,
        uniqueCategories
      };
    },
    generateComparisonSummary(products) {
      const priceComparison = this.comparePrice(products);
      const ratingComparison = this.compareRating(products);

      const cheapestProduct = products.find(p => p._id === priceComparison.cheapest);
      const highestRatedProduct = products.find(p => p._id === ratingComparison.highestRated);

      return {
        cheapest: {
          product: cheapestProduct?.name,
          id: cheapestProduct?._id
        },
        highestRated: {
          product: highestRatedProduct?.name,
          id: highestRatedProduct?._id,
          rating: highestRatedProduct?.averageRating
        },
        priceRange: {
          min: priceComparison.data.reduce((min, p) =>
            Math.min(min, p.priceRange.min), Infinity),
          max: priceComparison.data.reduce((max, p) =>
            Math.max(max, p.priceRange.max), 0)
        },
        totalProducts: products.length
      };
    },
    formatProductForComparison(product) {
      return {
        id: product._id,
        name: product.name,
        slug: product.slug,
        description: product.description,
        images: product.images,
        categories: product.categoryIds,
        rating: {
          average: product.averageRating,
          count: product.totalReviews
        },
        price: this.calculatePriceRange(product.variants),
        availability: this.calculateAvailability(product.variants),
        attributes: this.normalizeAttributes(product.attributes),
        variants: product.variants?.length || 0,
        specifications: this.extractSpecifications(product)
      };
    },
    findDifferences(product1, product2) {
      const diffs = [];

      // Price differences
      const price1 = this.calculatePriceRange(product1.variants);
      const price2 = this.calculatePriceRange(product2.variants);

      if ( Math.abs(price1.average - price2.average) > 10000 ) { // 10k VND threshold
        diffs.push({
          field: 'price',
          product1: price1,
          product2: price2,
          significance: 'high'
        });
      }

      // Rating differences
      if ( Math.abs((product1.averageRating || 0) - (product2.averageRating || 0)) > 0.5 ) {
        diffs.push({
          field: 'rating',
          product1: product1.averageRating || 0,
          product2: product2.averageRating || 0,
          significance: 'medium'
        });
      }

      // Category differences
      const categories1 = product1.categoryIds?.map(c => c._id) || [];
      const categories2 = product2.categoryIds?.map(c => c._id) || [];

      const categoryDiff = categories1.filter(c => !categories2.includes(c))
        .concat(categories2.filter(c => !categories1.includes(c)));

      if ( categoryDiff.length > 0 ) {
        diffs.push({
          field: 'categories',
          product1: categories1,
          product2: categories2,
          significance: 'low'
        });
      }

      return diffs;
    },
    findSimilarities(product1, product2) {
      const similarities = [];

      // Common categories
      const categories1 = product1.categoryIds?.map(c => c._id) || [];
      const categories2 = product2.categoryIds?.map(c => c._id) || [];
      const commonCategories = categories1.filter(c => categories2.includes(c));

      if ( commonCategories.length > 0 ) {
        similarities.push({
          field: 'categories',
          commonValues: commonCategories,
          description: 'Cùng danh mục sản phẩm'
        });
      }

      // Similar price range
      const price1 = this.calculatePriceRange(product1.variants);
      const price2 = this.calculatePriceRange(product2.variants);

      if ( Math.abs(price1.average - price2.average) <= 50000 ) { // 50k VND
        similarities.push({
          field: 'price',
          description: 'Mức giá tương đương'
        });
      }

      return similarities;
    },
    generateRecommendation(product1, product2) {
      const price1 = this.calculatePriceRange(product1.variants);
      const price2 = this.calculatePriceRange(product2.variants);
      const rating1 = product1.averageRating || 0;
      const rating2 = product2.averageRating || 0;

      let recommendation = '';

      if ( price1.average < price2.average && rating1 >= rating2 ) {
        recommendation = `${product1.name} có giá tốt hơn và đánh giá tương đương hoặc cao hơn`;
      } else if ( price1.average > price2.average && rating1 > rating2 ) {
        recommendation = `${product1.name} có giá cao hơn nhưng chất lượng tốt hơn`;
      } else if ( price1.average < price2.average ) {
        recommendation = `${product1.name} có giá tốt hơn`;
      } else if ( rating1 > rating2 ) {
        recommendation = `${product1.name} có đánh giá tốt hơn`;
      } else if ( price2.average < price1.average && rating2 >= rating1 ) {
        recommendation = `${product2.name} có giá tốt hơn và đánh giá tương đương hoặc cao hơn`;
      } else {
        recommendation = 'Cả hai sản phẩm đều có ưu điểm riêng, hãy xem xét nhu cầu cụ thể';
      }

      return recommendation;
    },
    buildTableRow(field, products) {
      switch ( field ) {
        case 'name':
          return ['Tên sản phẩm', ...products.map(p => p.name)];

        case 'price':
          return ['Giá', ...products.map(p => {
            const range = this.calculatePriceRange(p.variants);
            return range.min === range.max ?
              `${range.min.toLocaleString()} VND` :
              `${range.min.toLocaleString()} - ${range.max.toLocaleString()} VND`;
          })];

        case 'rating':
          return ['Đánh giá', ...products.map(p =>
            `${p.averageRating || 0}/5 (${p.totalReviews || 0} reviews)`
          )];

        case 'availability':
          return ['Tình trạng', ...products.map(p => {
            const avail = this.calculateAvailability(p.variants);
            return avail.inStock ? `Còn hàng (${avail.totalStock})` : 'Hết hàng';
          })];

        case 'categories':
          return ['Danh mục', ...products.map(p =>
            p.categoryIds?.map(c => c.name).join(', ') || 'N/A'
          )];

        default:
          return null;
      }
    },
    generateComparisonId(productIds) {
      const sortedIds = [...productIds].sort();
      const hash = require('crypto')
        .createHash('md5')
        .update(sortedIds.join('-'))
        .digest('hex');

      return hash.substring(0, 8);
    },

    convertToExcel(data, columns = null) {
      if ( !data || data.length === 0 ) {
        data = [{}];
      }

      const worksheet = XLSX.utils.json_to_sheet(data, {
        header: columns
      });

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Products');

      return XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    },

    // Parse Excel
    parseExcel(buffer) {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      return XLSX.utils.sheet_to_json(worksheet, {
        header: 1,
        defval: '',
        blankrows: false
      }).slice(1).map(row => {
        // Convert array to object using first row as headers
        const headers = XLSX.utils.sheet_to_json(worksheet, { header: 1 })[0];
        const obj = {};
        headers.forEach((header, index) => {
          obj[header] = row[index] || '';
        });
        return obj;
      });
    },

    // Validate import data
    validateImportData(data) {
      const errors = [];
      const warnings = [];
      const criticalErrors = [];
      const validData = [];

      data.forEach((row, index) => {
        const rowNum = index + 2; // +2 because index starts at 0 and we skip header
        const rowErrors = [];

        // Required fields validation
        if ( !row['Tên sản phẩm*'] || row['Tên sản phẩm*'].trim() === '' ) {
          rowErrors.push(`Dòng ${rowNum}: Tên sản phẩm là bắt buộc`);
        }

        if ( !row['Slug*'] || row['Slug*'].trim() === '' ) {
          rowErrors.push(`Dòng ${rowNum}: Slug là bắt buộc`);
        }

        if ( !row['Danh mục*'] || row['Danh mục*'].trim() === '' ) {
          rowErrors.push(`Dòng ${rowNum}: Danh mục là bắt buộc`);
        }

        // Variant validation if included
        if ( row['Variant SKU*'] !== undefined ) {
          if ( !row['Variant SKU*'] || row['Variant SKU*'].trim() === '' ) {
            rowErrors.push(`Dòng ${rowNum}: Variant SKU là bắt buộc`);
          }

          if ( !row['Variant Name*'] || row['Variant Name*'].trim() === '' ) {
            rowErrors.push(`Dòng ${rowNum}: Variant Name là bắt buộc`);
          }

          if ( !row['Giá*'] || isNaN(parseFloat(row['Giá*'])) ) {
            rowErrors.push(`Dòng ${rowNum}: Giá phải là số`);
          }
        }

        // Data type validation
        const statusValue = row['Trạng thái (1=Hoạt động, 0=Không hoạt động)'];
        if ( statusValue !== '' && statusValue !== '1' && statusValue !== '0' && statusValue !== 1 && statusValue !== 0 ) {
          warnings.push(`Dòng ${rowNum}: Trạng thái phải là 1 hoặc 0, sẽ dùng giá trị mặc định là 1`);
        }

        if ( rowErrors.length > 0 ) {
          errors.push(...rowErrors);
          if ( rowErrors.some(err => err.includes('là bắt buộc')) ) {
            criticalErrors.push(...rowErrors);
          }
        } else {
          validData.push({ row, rowNum });
        }
      });

      return {
        errors,
        warnings,
        criticalErrors,
        validData,
        validRecords: validData.length
      };
    },

    // Process import
    async processImport(ctx, validData, options) {
      const results = {
        created: 0,
        updated: 0,
        skipped: 0,
        failed: 0,
        details: []
      };

      for ( const { row, rowNum } of validData ) {
        try {
          // Check if product exists
          const existingProduct = await ctx.call('products.find', {
            query: { slug: row['Slug*'] },
            limit: 1
          });

          let productData = this.convertRowToProductData(row);
          let product;

          if ( existingProduct.length > 0 ) {
            if ( options.updateExisting ) {
              // Update existing product
              product = await ctx.call('products.update', {
                id: existingProduct[0]._id,
                ...productData
              });
              results.updated++;
              results.details.push(`Dòng ${rowNum}: Cập nhật sản phẩm ${productData.name}`);
            } else {
              results.skipped++;
              results.details.push(`Dòng ${rowNum}: Bỏ qua sản phẩm đã tồn tại ${productData.name}`);
              continue;
            }
          } else {
            // Create new product
            product = await ctx.call('products.create', productData);
            results.created++;
            results.details.push(`Dòng ${rowNum}: Tạo mới sản phẩm ${productData.name}`);
          }

          // Handle variants if included
          if ( row['Variant SKU*'] ) {
            await this.processVariantImport(ctx, product._id, row, rowNum, results, options);
          }

        } catch ( error ) {
          results.failed++;
          results.details.push(`Dòng ${rowNum}: Lỗi - ${error.message}`);

          if ( !options.skipErrors ) {
            throw error;
          }
        }
      }

      return results;
    },

    // Process variant import
    async processVariantImport(ctx, productId, row, rowNum, results, options) {
      try {
        const variantData = this.convertRowToVariantData(row, productId);

        // Check if variant exists
        const existingVariant = await ctx.call('variants.find', {
          query: { sku: variantData.sku },
          limit: 1
        });

        if ( existingVariant.length > 0 ) {
          if ( options.updateExisting ) {
            await ctx.call('variants.update', {
              id: existingVariant[0]._id,
              ...variantData
            });
            results.details.push(`Dòng ${rowNum}: Cập nhật variant ${variantData.sku}`);
          } else {
            results.details.push(`Dòng ${rowNum}: Bỏ qua variant đã tồn tại ${variantData.sku}`);
          }
        } else {
          const variant = await ctx.call('variants.create', variantData);
          results.details.push(`Dòng ${rowNum}: Tạo mới variant ${variantData.sku}`);

          // Create inventory if stock is provided
          const stock = parseInt(row['Tồn kho']) || 0;
          if ( stock > 0 ) {
            await ctx.call('inventories.create', {
              variantId: variant._id,
              totalStock: stock,
              availableStock: stock
            });
          }
        }
      } catch ( error ) {
        results.details.push(`Dòng ${rowNum}: Lỗi variant - ${error.message}`);
      }
    },

    // Convert row to product data
    convertRowToProductData(row) {
      const productData = {
        name: row['Tên sản phẩm*'].trim(),
        slug: row['Slug*'].trim(),
        description: row['Mô tả'] || '',
        isActive: this.convertBoolean(row['Trạng thái (1=Hoạt động, 0=Không hoạt động)'], true),
        isFeatured: this.convertBoolean(row['Nổi bật (1=Có, 0=Không)'], false),
        isNewProduct: this.convertBoolean(row['Sản phẩm mới (1=Có, 0=Không)'], false),
        metaTitle: row['Meta Title'] || '',
        metaDescription: row['Meta Description'] || '',
        metaKeywords: row['Meta Keywords'] || ''
      };

      // Handle attributes
      const attributes = [];
      for ( let i = 1; i <= 10; i++ ) { // Support up to 10 attributes
        const attrName = row[`Thuộc tính ${i} - Tên`];
        const attrValue = row[`Thuộc tính ${i} - Giá trị`];

        if ( attrName && attrValue ) {
          attributes.push({
            name: attrName.trim(),
            values: attrValue.split(',').map(v => v.trim())
          });
        }
      }

      if ( attributes.length > 0 ) {
        productData.attributes = attributes;
      }

      return productData;
    },

    // Convert row to variant data
    convertRowToVariantData(row, productId) {
      const variantData = {
        productId,
        sku: row['Variant SKU*'].trim(),
        name: row['Variant Name*'].trim(),
        price: parseFloat(row['Giá*']) || 0,
        compareAtPrice: parseFloat(row['Giá so sánh']) || undefined,
        cost: parseFloat(row['Giá vốn']) || undefined,
        weight: parseFloat(row['Trọng lượng (gram)']) || undefined
      };

      // Handle dimensions
      const length = parseFloat(row['Chiều dài (cm)']);
      const width = parseFloat(row['Chiều rộng (cm)']);
      const height = parseFloat(row['Chiều cao (cm)']);

      if ( length || width || height ) {
        variantData.dimensions = {
          length: length || 0,
          width: width || 0,
          height: height || 0
        };
      }

      // Handle attributes
      const attributes = [];
      for ( let i = 1; i <= 10; i++ ) {
        const attrName = row[`V.Attr ${i} - Tên`];
        const attrValue = row[`V.Attr ${i} - Giá trị`];

        if ( attrName && attrValue ) {
          attributes.push({
            name: attrName.trim(),
            value: attrValue.trim()
          });
        }
      }

      if ( attributes.length > 0 ) {
        variantData.attributes = attributes;
      }

      return variantData;
    },

    // Convert boolean values
    convertBoolean(value, defaultValue) {
      if ( value === 1 || value === '1' || value === true || value === 'true' ) {
        return true;
      } else if ( value === 0 || value === '0' || value === false || value === 'false' ) {
        return false;
      }
      return defaultValue;
    },

    // Get import instructions
    getImportInstructions() {
      return {
        requiredFields: [
          'Tên sản phẩm*',
          'Slug*',
          'Danh mục*'
        ],
        variantRequiredFields: [
          'Variant SKU*',
          'Variant Name*',
          'Giá*'
        ],
        booleanFields: {
          'Trạng thái': '1 = Hoạt động, 0 = Không hoạt động',
          'Nổi bật': '1 = Có, 0 = Không',
          'Sản phẩm mới': '1 = Có, 0 = Không'
        },
        notes: [
          'Các cột có dấu * là bắt buộc',
          'Slug phải là duy nhất',
          'Danh mục phải tồn tại trong hệ thống',
          'Giá phải là số',
          'Thuộc tính có thể có nhiều giá trị, cách nhau bởi dấu phẩy',
          'Nếu có Variant SKU thì phải có đầy đủ thông tin variant'
        ]
      };
    }
  },
  events: {},
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  }
};
